@echo off
echo ============================================
echo Network Access Fix Tool
echo ============================================
echo.
echo Your Django app works on localhost:8000 but not on external IPs
echo This tool will fix network access issues step by step.
echo.

REM Check if running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo ⚠️ WARNING: Not running as Administrator
    echo Some fixes may require administrator privileges
    echo.
    echo For best results, right-click this file and "Run as administrator"
    echo.
    set /p continue="Continue anyway? (Y/N): "
    if /i not "%continue%"=="Y" exit /b 0
)

echo ============================================
echo STEP 1: CHECKING CURRENT SERVER STATUS
echo ============================================

echo Checking if Django server is running...
netstat -an | findstr :8000 >nul
if errorlevel 1 (
    echo ❌ No server found on port 8000
    echo Please start your Django server first:
    echo cd C:\WebApps\WaterBilling
    echo venv\Scripts\activate
    echo python manage.py runserver 0.0.0.0:8000
    echo.
    echo Then run this script again.
    pause
    exit /b 1
) else (
    echo ✅ Server is running on port 8000
)

echo.
echo Checking server binding...
netstat -an | findstr :8000
echo.

REM Check if bound to 0.0.0.0 (all interfaces)
netstat -an | findstr "0.0.0.0:8000" >nul
if errorlevel 1 (
    echo ❌ PROBLEM FOUND: Server is not bound to all interfaces
    echo.
    echo Your server is probably bound to 127.0.0.1 (localhost only)
    echo This prevents external access.
    echo.
    echo SOLUTION: Restart your server with correct binding
    echo.
    echo 1. Stop current server (Ctrl+C in the server window)
    echo 2. Start with: python manage.py runserver 0.0.0.0:8000
    echo.
    echo The 0.0.0.0 part is crucial - it allows external access
    echo.
    set /p fix_binding="Would you like me to help restart the server correctly? (Y/N): "
    if /i "%fix_binding%"=="Y" goto :restart_server
    echo.
    echo Please restart your server manually with the correct binding.
    pause
    exit /b 1
) else (
    echo ✅ Server is correctly bound to all interfaces (0.0.0.0:8000)
)

echo.
echo ============================================
echo STEP 2: WINDOWS FIREWALL CONFIGURATION
echo ============================================

echo Checking Windows Firewall rules...
netsh advfirewall firewall show rule name="Django Web App" >nul 2>&1
if errorlevel 1 (
    echo ❌ Firewall rule not found
    echo Creating Windows Firewall rule...
    
    netsh advfirewall firewall add rule name="Django Web App" dir=in action=allow protocol=TCP localport=8000 profile=any
    if errorlevel 1 (
        echo ❌ Failed to create firewall rule (need admin rights)
        echo.
        echo MANUAL SOLUTION:
        echo 1. Press Windows + R
        echo 2. Type: wf.msc
        echo 3. Click "Inbound Rules" → "New Rule"
        echo 4. Type: Port → TCP → Specific Local Ports: 8000
        echo 5. Action: Allow → Apply to all profiles
        echo 6. Name: Django Web App
        echo.
        set /p manual_fw="Have you created the firewall rule manually? (Y/N): "
        if /i not "%manual_fw%"=="Y" (
            echo Please create the firewall rule and run this script again.
            pause
            exit /b 1
        )
    ) else (
        echo ✅ Firewall rule created for port 8000
    )
    
    REM Also create rule for port 8080
    netsh advfirewall firewall add rule name="Django Web App 8080" dir=in action=allow protocol=TCP localport=8080 profile=any >nul 2>&1
    echo ✅ Firewall rule created for port 8080
    
) else (
    echo ✅ Firewall rule already exists
)

echo.
echo Checking firewall status...
netsh advfirewall show allprofiles state
echo.

echo ============================================
echo STEP 3: NETWORK INTERFACE CHECK
echo ============================================

echo Checking network interfaces...
ipconfig | findstr "IPv4"
echo.

echo Checking if server IP matches expected IPs...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr "IPv4"') do (
    set ip=%%a
    set ip=!ip: =!
    echo Found IP: !ip!
    if "!ip!"=="**************" echo ✅ Found matching IP: **************
    if "!ip!"=="**************" echo ✅ Found matching IP: **************
)

echo.
echo ============================================
echo STEP 4: PORT CONNECTIVITY TEST
echo ============================================

echo Testing if port 8000 is accessible from network...
echo.

REM Test local connectivity first
echo Testing localhost connectivity...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000' -UseBasicParsing -TimeoutSec 5; Write-Host 'Localhost test: SUCCESS (Status:' $response.StatusCode ')' } catch { Write-Host 'Localhost test: FAILED -' $_.Exception.Message }"

echo.
echo Testing network interface connectivity...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr "IPv4" ^| findstr -v "127.0.0.1"') do (
    set ip=%%a
    set ip=!ip: =!
    echo Testing http://!ip!:8000
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://!ip!:8000' -UseBasicParsing -TimeoutSec 5; Write-Host 'Network test (!ip!): SUCCESS' } catch { Write-Host 'Network test (!ip!): FAILED -' $_.Exception.Message }"
)

echo.
echo ============================================
echo STEP 5: ROUTER/NETWORK CONFIGURATION CHECK
echo ============================================

echo.
echo IMPORTANT: External access requires router configuration
echo.
echo Your server is accessible on the local network, but for external
echo access (from internet), your router must be configured.
echo.
echo REQUIRED ROUTER CONFIGURATION:
echo ===============================
echo.
echo Contact your network administrator or IT person to configure:
echo.
echo 1. PORT FORWARDING:
echo    - External Port 8000 → Internal IP:8000
echo    - External Port 8080 → Internal IP:8080
echo.
echo 2. FIREWALL RULES:
echo    - Allow incoming connections on ports 8000 and 8080
echo    - Allow traffic to your server's internal IP
echo.
echo 3. NAT CONFIGURATION:
echo    - Map external IPs to your server's internal IP
echo    - **************:8000 → [Your Server Internal IP]:8000
echo    - **************:8080 → [Your Server Internal IP]:8080
echo.

echo ============================================
echo STEP 6: TESTING AND VERIFICATION
echo ============================================

echo.
echo TESTING CHECKLIST:
echo ==================
echo.
echo ✅ Local Test (should work):
echo    http://localhost:8000
echo.
echo ✅ Internal Network Test (should work if firewall is configured):
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr "IPv4" ^| findstr -v "127.0.0.1"') do (
    set ip=%%a
    set ip=!ip: =!
    echo    http://!ip!:8000
)
echo.
echo ⚠️ External Test (requires router configuration):
echo    http://**************:8000
echo    http://**************:8080
echo.

echo ============================================
echo STEP 7: IMMEDIATE SOLUTIONS
echo ============================================

echo.
echo SOLUTION 1: Test from another computer on same network
echo =====================================================
echo From another computer on the same local network, try:
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr "IPv4" ^| findstr -v "127.0.0.1"') do (
    set ip=%%a
    set ip=!ip: =!
    echo http://!ip!:8000
)
echo.
echo If this works, the issue is router configuration.
echo If this doesn't work, the issue is Windows Firewall.
echo.

echo SOLUTION 2: Temporary external access test
echo ==========================================
echo You can temporarily test external access using ngrok:
echo 1. Download ngrok from: https://ngrok.com/download
echo 2. Run: ngrok http 8000
echo 3. Use the provided public URL for testing
echo.

echo SOLUTION 3: Check if server is actually accessible
echo ==================================================
echo Run this test from another computer:
echo telnet ************** 8000
echo.
echo If connection is refused: Router/firewall issue
echo If connection times out: Network routing issue
echo If connection succeeds: Django configuration issue
echo.

goto :end

:restart_server
echo.
echo ============================================
echo RESTARTING SERVER WITH CORRECT BINDING
echo ============================================
echo.
echo I'll help you restart the server with correct network binding.
echo.
echo Please follow these steps:
echo.
echo 1. Go to your Django server window
echo 2. Press Ctrl+C to stop the current server
echo 3. Then run these commands:
echo.
echo cd C:\WebApps\WaterBilling
echo venv\Scripts\activate
echo python manage.py runserver 0.0.0.0:8000
echo.
echo The key is using 0.0.0.0:8000 instead of just 8000
echo This allows external connections.
echo.
echo After restarting, run this script again to verify.
echo.
pause
exit /b 0

:end
echo.
echo ============================================
echo SUMMARY AND NEXT STEPS
echo ============================================
echo.
echo CURRENT STATUS:
echo ✅ Django application is working
echo ✅ Local access works (localhost:8000)
echo ✅ Supabase integration is working
echo.
echo FOR EXTERNAL ACCESS (**************:8000):
echo.
echo 1. ✅ Windows Firewall: Configured
echo 2. ⚠️ Router Configuration: REQUIRED
echo 3. ⚠️ Network Setup: Contact IT administrator
echo.
echo IMMEDIATE ACTION REQUIRED:
echo =========================
echo.
echo Contact your network administrator with this information:
echo.
echo "Please configure port forwarding on the router:
echo  - Forward external port 8000 to internal IP [your server IP]:8000
echo  - Forward external port 8080 to internal IP [your server IP]:8080
echo  - Allow incoming connections on these ports
echo  - Map external IPs ************** and ************** to this server"
echo.
echo TESTING:
echo ========
echo 1. Test local: http://localhost:8000 ✅
echo 2. Test internal network from another computer
echo 3. Test external after router configuration
echo.
echo Your Django application is working perfectly!
echo The only remaining step is network/router configuration.
echo.
pause
