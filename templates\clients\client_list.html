{% extends 'base.html' %}
{% load currency_filters %}

{% block title %}Clients - Water Billing Management System{% endblock %}

{% block page_title %}Clients{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item active">Clients</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Client Management</h3>
                <div class="card-tools">
                    <div class="input-group input-group-sm" style="width: 250px;">
                        <form method="get" class="d-flex">
                            <input type="text" name="search" class="form-control float-right" placeholder="Search clients..." value="{{ request.GET.search }}">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-default">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <a href="{% url 'clients:client_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Client
                    </a>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Category</th>
                                <th>Meter Number</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in clients %}
                            <tr>
                                <td><strong>{{ client.code }}</strong></td>
                                <td>{{ client.get_full_name }}</td>
                                <td>
                                    {% if client.contact %}{{ client.contact }}{% endif %}
                                    {% if client.email %}<br><small class="text-muted">{{ client.email }}</small>{% endif %}
                                </td>
                                <td>
                                    <span class="badge badge-info">{{ client.category.name }}</span>
                                    <br><small class="text-muted">{{ client.category.price_per_cubic_meter|currency }}/m³</small>
                                </td>
                                <td>{{ client.meter_number }}</td>
                                <td>
                                    {% if client.status %}
                                        <span class="badge badge-success">Active</span>
                                    {% else %}
                                        <span class="badge badge-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'clients:client_detail' client.pk %}" class="btn btn-sm btn-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'clients:client_billing_history' client.pk %}" class="btn btn-sm btn-success" title="Billing History">
                                            <i class="fas fa-history"></i>
                                        </a>
                                        <a href="{% url 'clients:client_update' client.pk %}" class="btn btn-sm btn-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'clients:client_delete' client.pk %}" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this client?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="text-center">No clients found.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <div class="row">
                    <div class="col-sm-12 col-md-5">
                        <div class="dataTables_info">
                            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-7">
                        <div class="dataTables_paginate paging_simple_numbers">
                            <ul class="pagination">
                                {% if page_obj.has_previous %}
                                    <li class="paginate_button page-item previous">
                                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" class="page-link">Previous</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="paginate_button page-item active">
                                            <a href="#" class="page-link">{{ num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="paginate_button page-item">
                                            <a href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" class="page-link">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="paginate_button page-item next">
                                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" class="page-link">Next</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
