{% extends 'base.html' %}

{% block title %}System Settings - Water Billing Management System{% endblock %}

{% block page_title %}System Settings{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item active">System Settings</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">System Information</h3>
            </div>
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="card-body">
                    <div class="form-group">
                        <label for="{{ form.name.id_for_label }}">System Name *</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.short_name.id_for_label }}">Short Name *</label>
                        {{ form.short_name }}
                        {% if form.short_name.errors %}
                            <div class="text-danger">
                                {% for error in form.short_name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.logo.id_for_label }}">Logo</label>
                        {{ form.logo }}
                        {% if system_info.logo %}
                            <div class="mt-2">
                                <img src="{{ system_info.logo.url }}" alt="Current Logo" style="max-height: 100px;">
                                <p class="text-muted">Current logo</p>
                            </div>
                        {% endif %}
                        {% if form.logo.errors %}
                            <div class="text-danger">
                                {% for error in form.logo.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.address.id_for_label }}">Address</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-danger">
                                {% for error in form.address.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.phone.id_for_label }}">Phone</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger">
                                        {% for error in form.phone.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.email.id_for_label }}">Email</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger">
                                        {% for error in form.email.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.website.id_for_label }}">Website</label>
                        {{ form.website }}
                        {% if form.website.errors %}
                            <div class="text-danger">
                                {% for error in form.website.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update System Information
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">Current System Info</h3>
            </div>
            <div class="card-body">
                {% if system_info.logo %}
                    <div class="text-center mb-3">
                        <img src="{{ system_info.logo.url }}" alt="Logo" style="max-height: 80px;">
                    </div>
                {% endif %}
                <p><strong>Name:</strong> {{ system_info.name }}</p>
                <p><strong>Short Name:</strong> {{ system_info.short_name }}</p>
                {% if system_info.address %}
                    <p><strong>Address:</strong><br>{{ system_info.address|linebreaks }}</p>
                {% endif %}
                {% if system_info.phone %}
                    <p><strong>Phone:</strong> {{ system_info.phone }}</p>
                {% endif %}
                {% if system_info.email %}
                    <p><strong>Email:</strong> {{ system_info.email }}</p>
                {% endif %}
                {% if system_info.website %}
                    <p><strong>Website:</strong> <a href="{{ system_info.website }}" target="_blank">{{ system_info.website }}</a></p>
                {% endif %}
                <p><strong>Last Updated:</strong> {{ system_info.updated_at|date:"M d, Y H:i" }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
