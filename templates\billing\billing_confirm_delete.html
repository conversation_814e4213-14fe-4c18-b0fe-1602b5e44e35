{% extends 'base.html' %}

{% block title %}Delete Billing - Water Billing Management System{% endblock %}

{% block page_title %}Delete Billing{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'billing:billing_list' %}">Billing</a></li>
<li class="breadcrumb-item active">Delete</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card card-danger">
            <div class="card-header">
                <h3 class="card-title">Confirm Billing Deletion</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                
                <p>Are you sure you want to delete this billing record?</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Client Information</h5>
                            </div>
                            <div class="card-body">
                                <ul>
                                    <li><strong>Client:</strong> {{ object.client.get_full_name }}</li>
                                    <li><strong>Code:</strong> {{ object.client.code }}</li>
                                    <li><strong>Category:</strong> {{ object.client.category.name }}</li>
                                    <li><strong>Meter:</strong> {{ object.client.meter_number }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Billing Details</h5>
                            </div>
                            <div class="card-body">
                                <ul>
                                    <li><strong>Billing Month:</strong> {{ object.billing_month|date:"F Y" }}</li>
                                    <li><strong>Consumption:</strong> {{ object.consumption }} m³</li>
                                    <li><strong>Total Amount:</strong> ${{ object.total_amount|floatformat:2 }}</li>
                                    <li><strong>Status:</strong> 
                                        {% if object.status == 'paid' %}
                                            <span class="badge badge-success">Paid</span>
                                        {% elif object.status == 'pending' %}
                                            <span class="badge badge-warning">Pending</span>
                                        {% else %}
                                            <span class="badge badge-danger">Overdue</span>
                                        {% endif %}
                                    </li>
                                    <li><strong>Due Date:</strong> {{ object.due_date|date:"M d, Y" }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Billing Breakdown</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tr>
                                    <th>Previous Reading:</th>
                                    <td>{{ object.previous_reading }} m³</td>
                                </tr>
                                <tr>
                                    <th>Current Reading:</th>
                                    <td>{{ object.current_reading }} m³</td>
                                </tr>
                                <tr>
                                    <th>Consumption:</th>
                                    <td><strong>{{ object.consumption }} m³</strong></td>
                                </tr>
                                <tr>
                                    <th>Rate per m³:</th>
                                    <td>${{ object.rate_per_cubic_meter|floatformat:2 }}</td>
                                </tr>
                                <tr>
                                    <th>Amount:</th>
                                    <td>${{ object.amount|floatformat:2 }}</td>
                                </tr>
                                {% if object.penalty > 0 %}
                                <tr>
                                    <th>Penalty:</th>
                                    <td class="text-danger">${{ object.penalty|floatformat:2 }}</td>
                                </tr>
                                {% endif %}
                                <tr class="bg-light">
                                    <th>Total:</th>
                                    <th>${{ object.total_amount|floatformat:2 }}</th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                {% if object.status == 'paid' %}
                <div class="alert alert-danger mt-3">
                    <i class="fas fa-exclamation-circle"></i>
                    <strong>Important:</strong> This billing record is marked as PAID. 
                    Deleting it will remove the payment record and may affect financial reports.
                    {% if object.paid_date %}
                        <br><strong>Paid Date:</strong> {{ object.paid_date|date:"M d, Y" }}
                    {% endif %}
                </div>
                {% endif %}
                
                {% if object.notes %}
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Notes</h5>
                    </div>
                    <div class="card-body">
                        {{ object.notes|linebreaks }}
                    </div>
                </div>
                {% endif %}
            </div>
            
            <div class="card-footer">
                <form method="post">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Yes, Delete Billing Record
                    </button>
                    <a href="{% url 'billing:billing_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
