{% extends 'base.html' %}

{% block title %}{% if object %}Edit Client{% else %}Add Client{% endif %} - Water Billing Management System{% endblock %}

{% block page_title %}{% if object %}Edit Client{% else %}Add New Client{% endif %}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'clients:client_list' %}">Clients</a></li>
<li class="breadcrumb-item active">{% if object %}Edit{% else %}Add{% endif %}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">Client Information</h3>
            </div>
            <form method="post">
                {% csrf_token %}
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.code.id_for_label }}">Client Code *</label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                    <div class="text-danger">
                                        {% for error in form.code.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.meter_number.id_for_label }}">Meter Number *</label>
                                {{ form.meter_number }}
                                {% if form.meter_number.errors %}
                                    <div class="text-danger">
                                        {% for error in form.meter_number.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="{{ form.firstname.id_for_label }}">First Name *</label>
                                {{ form.firstname }}
                                {% if form.firstname.errors %}
                                    <div class="text-danger">
                                        {% for error in form.firstname.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="{{ form.middlename.id_for_label }}">Middle Name</label>
                                {{ form.middlename }}
                                {% if form.middlename.errors %}
                                    <div class="text-danger">
                                        {% for error in form.middlename.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="{{ form.lastname.id_for_label }}">Last Name *</label>
                                {{ form.lastname }}
                                {% if form.lastname.errors %}
                                    <div class="text-danger">
                                        {% for error in form.lastname.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.contact.id_for_label }}">Contact Number</label>
                                {{ form.contact }}
                                {% if form.contact.errors %}
                                    <div class="text-danger">
                                        {% for error in form.contact.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.email.id_for_label }}">Email Address</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger">
                                        {% for error in form.email.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.address.id_for_label }}">Address *</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-danger">
                                {% for error in form.address.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.category.id_for_label }}">Billing Category *</label>
                                {{ form.category }}
                                {% if form.category.errors %}
                                    <div class="text-danger">
                                        {% for error in form.category.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check mt-4">
                                    {{ form.status }}
                                    <label class="form-check-label" for="{{ form.status.id_for_label }}">
                                        Active Client
                                    </label>
                                </div>
                                {% if form.status.errors %}
                                    <div class="text-danger">
                                        {% for error in form.status.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> {% if object %}Update{% else %}Save{% endif %}
                    </button>
                    <a href="{% url 'clients:client_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">Help</h3>
            </div>
            <div class="card-body">
                <p><strong>Client Code:</strong> Enter a unique identifier for this client.</p>
                <p><strong>Meter Number:</strong> The unique water meter identification number.</p>
                <p><strong>Personal Information:</strong> Complete name and contact details.</p>
                <p><strong>Category:</strong> Select the appropriate billing category which determines the rate per cubic meter.</p>
                <p><strong>Active:</strong> Uncheck to deactivate this client account.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
