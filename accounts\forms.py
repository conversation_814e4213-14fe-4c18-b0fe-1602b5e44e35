from django import forms
from django.contrib.auth.forms import UserCreationForm
from .models import User, SystemInfo

class RegisterForm(UserCreationForm):
    email = forms.EmailField(required=True)
    phone = forms.CharField(max_length=20, required=False)
    address = forms.CharField(widget=forms.Textarea, required=False)
    user_type = forms.ChoiceField(choices=User.USER_TYPE_CHOICES, initial='customer')
    
    class Meta:
        model = User
        fields = ('username', 'email', 'password1', 'password2', 
                  'first_name', 'last_name', 'phone', 'address', 'user_type')
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply consistent styling to all fields
        self.fields['username'].widget.attrs.update({
            'class': 'form-control form-control-lg',
            'placeholder': 'Enter username',
            'style': 'font-size: 14px; color: #333; font-weight: 500;'
        })
        self.fields['email'].widget.attrs.update({
            'class': 'form-control form-control-lg',
            'placeholder': 'Enter email address',
            'style': 'font-size: 14px; color: #333; font-weight: 500;'
        })
        self.fields['first_name'].widget.attrs.update({
            'class': 'form-control form-control-lg',
            'placeholder': 'Enter first name',
            'style': 'font-size: 14px; color: #333; font-weight: 500;'
        })
        self.fields['last_name'].widget.attrs.update({
            'class': 'form-control form-control-lg',
            'placeholder': 'Enter last name',
            'style': 'font-size: 14px; color: #333; font-weight: 500;'
        })
        self.fields['user_type'].widget.attrs.update({
            'class': 'form-control form-control-lg',
            'style': 'font-size: 14px; color: #333; font-weight: 500;'
        })
        self.fields['phone'].widget.attrs.update({
            'class': 'form-control form-control-lg',
            'placeholder': 'Enter phone number',
            'style': 'font-size: 14px; color: #333; font-weight: 500;'
        })
        self.fields['address'].widget.attrs.update({
            'class': 'form-control form-control-lg',
            'rows': 3,
            'placeholder': 'Enter address',
            'style': 'font-size: 14px; color: #333; font-weight: 500;'
        })
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control form-control-lg',
            'placeholder': 'Enter password',
            'style': 'font-size: 14px; color: #333; font-weight: 500;'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control form-control-lg',
            'placeholder': 'Confirm password',
            'style': 'font-size: 14px; color: #333; font-weight: 500;'
        })
        
        # Add help text
        self.fields['user_type'].help_text = "Select the user role and access level"
        self.fields['username'].help_text = "Username for login (must be unique)"
        self.fields['email'].help_text = "Email address for notifications"

class UserForm(UserCreationForm):
    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'user_type', 'phone', 'address', 'is_active')
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'form-control form-control-lg',
                'placeholder': 'Enter username',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control form-control-lg',
                'placeholder': 'Enter email address',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'first_name': forms.TextInput(attrs={
                'class': 'form-control form-control-lg',
                'placeholder': 'Enter first name',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control form-control-lg',
                'placeholder': 'Enter last name',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'user_type': forms.Select(attrs={
                'class': 'form-control form-control-lg',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control form-control-lg',
                'placeholder': 'Enter phone number',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control form-control-lg',
                'rows': 3,
                'placeholder': 'Enter address',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'style': 'transform: scale(1.2);'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add help text for user types
        self.fields['user_type'].help_text = "Select the user role and access level"
        self.fields['username'].help_text = "Username for login (must be unique)"
        self.fields['email'].help_text = "Email address for notifications"
        self.fields['is_active'].help_text = "Uncheck to deactivate user account"
        
        # Style password fields
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control form-control-lg',
            'placeholder': 'Enter password',
            'style': 'font-size: 14px; color: #333; font-weight: 500;'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control form-control-lg',
            'placeholder': 'Confirm password',
            'style': 'font-size: 14px; color: #333; font-weight: 500;'
        })

class SystemInfoForm(forms.ModelForm):
    class Meta:
        model = SystemInfo
        fields = ['name', 'short_name', 'logo', 'address', 'phone', 'email', 'website']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control form-control-lg',
                'placeholder': 'System name',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'short_name': forms.TextInput(attrs={
                'class': 'form-control form-control-lg',
                'placeholder': 'Short name',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'logo': forms.FileInput(attrs={
                'class': 'form-control form-control-lg',
                'style': 'font-size: 14px;'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control form-control-lg',
                'rows': 3,
                'placeholder': 'System address',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control form-control-lg',
                'placeholder': 'Contact phone',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control form-control-lg',
                'placeholder': 'Contact email',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'website': forms.URLInput(attrs={
                'class': 'form-control form-control-lg',
                'placeholder': 'Website URL',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
        }