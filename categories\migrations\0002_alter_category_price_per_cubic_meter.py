# Generated by Django 5.2.4 on 2025-07-04 06:02

import django.core.validators
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("categories", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="category",
            name="price_per_cubic_meter",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Price per cubic meter in Indian Rupees (₹)",
                max_digits=10,
                validators=[django.core.validators.MinValueValidator(Decimal("0.01"))],
            ),
        ),
    ]
