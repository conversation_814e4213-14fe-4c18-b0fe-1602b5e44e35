{% extends 'base.html' %}
{% load static %}

{% block title %}Login - Water Billing Management System{% endblock %}

{% block login_content %}
<div class="login-page" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
    <div class="login-box">
        <div class="card card-outline card-primary">
            <div class="card-header text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px 8px 0 0;">
                <div class="mb-3">
                    <img src="{% static 'img/logo/logo.jpeg' %}" alt="Water Billing Management System" class="img-fluid" style="max-height: 60px; border-radius: 10px; border: 3px solid #FFD700;">
                </div>
                <h3 style="color: white; margin: 0;">
                    <b>Water Billing</b> Management System
                </h3>
                <p style="color: #E8F5E8; margin-top: 5px; font-size: 14px;">
                    Powered by <strong style="color: #FFD700;">Ankur Technologies</strong>
                </p>
            </div>
            <div class="card-body">
                <p class="login-box-msg">Sign in to start your session</p>
                
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    <div class="input-group mb-3">
                        <input type="text" name="username" class="form-control" placeholder="Username" required>
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-user"></span>
                            </div>
                        </div>
                    </div>
                    <div class="input-group mb-3">
                        <input type="password" name="password" class="form-control" placeholder="Password" required>
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-lock"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary btn-block">Sign In</button>
                        </div>
                    </div>
                </form>
                
                <div class="mt-3 text-center">
                    <p>Don't have an account? <a href="{% url 'accounts:register' %}" style="color: #FFD700; font-weight: bold;">Register here</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.login-page {
    display: flex;
    align-items: center;
    justify-content: center;
}
.login-box {
    width: 360px;
}

/* Enhanced message styling */
.alert {
    position: relative;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Style the register link */
a[href="{% url 'accounts:register' %}"] {
    transition: all 0.3s ease;
    text-decoration: none;
    font-weight: 600;
}

a[href="{% url 'accounts:register' %}"]:hover {
    color: #FFFFFF !important;
    text-shadow: 0 0 5px #FFD700;
    text-decoration: underline;
}
</style>

<script>
$(document).ready(function() {
    // Auto-hide alerts after 6 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 6000);

    // Handle close button clicks
    $('.close').click(function() {
        $(this).parent('.alert').fadeOut('fast');
    });

    // Make sure messages are visible and prominent
    $('.alert').each(function() {
        $(this).show().css('display', 'block');
    });
});
</script>

{% endblock %}