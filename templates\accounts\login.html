{% extends 'base.html' %}
{% load static %}

{% block title %}Login - Water Billing Management System{% endblock %}

{% block body_class %}login-page{% endblock %}

{% block extra_css %}
<style>
    body.login-page .main-header {
        display: none;
    }
</style>
{% endblock %}

{% block login_content %}
<div class="login-container d-flex align-items-center justify-content-center">
    <div class="login-card">
        <div class="card-header text-center">
            <div class="logo-container mb-3">
                <img src="{% static 'img/logo/logo.jpeg' %}" alt="Water Billing Management System" 
                     class="login-logo">
            </div>
            <h3 class="mb-1">Water Billing Management</h3>
            <p class="mb-0">Powered by <strong>Ankur Technologies</strong></p>
        </div>
        
        <div class="card-body">
            <p class="login-intro text-center">Sign in to your account</p>
            
            {% if messages %}
                <div class="mb-3">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            <form method="post" class="login-form">
                {% csrf_token %}
                <div class="form-group mb-4">
                    <div class="input-group">
                        <span class="input-group-icon">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" name="username" class="form-control" 
                               placeholder="Username" required>
                    </div>
                </div>
                
                <div class="form-group mb-4">
                    <div class="input-group">
                        <span class="input-group-icon">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" name="password" class="form-control" 
                               placeholder="Password" required>
                    </div>
                </div>
                
                <div class="form-submit mb-3">
                    <button type="submit" class="btn btn-primary w-100">
                        Sign In
                    </button>
                </div>
                
                <div class="register-link text-center">
                    <p class="mb-0">
                        Don't have an account? 
                        <a href="{% url 'accounts:register' %}" class="register-text">
                            Register here
                        </a>
                    </p>
                </div>
            </form>
            
            <div class="copyright text-center">
                <p class="mb-0">
                    &copy; {% now "Y" %} Ankur Technologies. All rights reserved.
                </p>
            </div>
        </div>
    </div>
</div>

<style>
    .login-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
    }
    
    .login-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        width: 100%;
        max-width: 420px;
        animation: fadeIn 0.6s ease-out;
    }
    
    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem 1.5rem;
        border: none;
    }
    
    .card-header h3 {
        color: white;
        font-weight: 700;
        font-size: 1.6rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        margin-bottom: 0.5rem;
    }
    
    .card-header p {
        color: rgba(255,255,255,0.85);
        font-size: 0.95rem;
        margin-bottom: 0;
    }
    
    .card-header p strong {
        color: #FFD700;
    }
    
    .login-logo {
        max-height: 75px;
        border-radius: 10px;
        border: 3px solid #FFD700;
        box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        padding: 5px;
        background: white;
    }
    
    .card-body {
        padding: 2rem;
    }
    
    .login-intro {
        font-size: 1.2rem;
        font-weight: 500;
        color: #555;
        margin-bottom: 1.8rem;
    }
    
    .input-group {
        margin-bottom: 1.2rem;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 3px 8px rgba(102, 126, 234, 0.15);
    }
    
    .input-group-icon {
        background: white;
        border: none;
        padding: 0 1rem;
        display: flex;
        align-items: center;
        color: #667eea;
        font-size: 1.1rem;
    }
    
    .form-control {
        border: none;
        border-left: 1px solid #eee !important;
        padding: 1rem;
        height: auto;
        font-size: 1rem;
    }
    
    .form-control:focus {
        box-shadow: none;
        border-color: #667eea;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 0.9rem;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.35);
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.45);
    }
    
    .register-link {
        margin-top: 1.8rem;
        padding-top: 1.5rem;
        border-top: 1px solid #eee;
    }
    
    .register-link p {
        color: #666;
        font-size: 0.95rem;
        margin-bottom: 0;
    }
    
    .register-text {
        color: #667eea;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s;
    }
    
    .register-text:hover {
        color: #5a6edc;
        text-decoration: underline;
    }
    
    .copyright {
        margin-top: 1.5rem;
        padding-top: 1.2rem;
        border-top: 1px solid #eee;
    }
    
    .copyright p {
        color: #888;
        font-size: 0.85rem;
        margin-bottom: 0;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    /* Responsive adjustments */
    @media (max-width: 576px) {
        .login-card {
            max-width: 95%;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .card-header {
            padding: 1.5rem 1rem;
        }
        
        .login-logo {
            max-height: 65px;
        }
        
        .card-header h3 {
            font-size: 1.4rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Auto-hide alerts after 6 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 6000);
        
        // Add focus effects to inputs
        $('.form-control').focus(function() {
            $(this).closest('.input-group').css('box-shadow', '0 3px 10px rgba(102, 126, 234, 0.25)');
        }).blur(function() {
            $(this).closest('.input-group').css('box-shadow', '0 3px 8px rgba(102, 126, 234, 0.15)');
        });
    });
</script>
{% endblock %}