{% extends 'base.html' %}

{% block title %}{% if object %}Edit Billing{% else %}Create Billing{% endif %} - Water Billing Management System{% endblock %}

{% block page_title %}{% if object %}Edit Billing{% else %}Create New Billing{% endif %}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'billing:billing_list' %}">Billing</a></li>
<li class="breadcrumb-item active">{% if object %}Edit{% else %}Create{% endif %}</li>
{% endblock %}

{% block content %}
<style>
/* Enhanced Form Styling for Better Visibility */
.billing-form-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.billing-form-container .form-label {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
    margin-bottom: 8px !important;
    display: block !important;
}

.billing-form-container .form-control {
    font-size: 15px !important;
    font-weight: 500 !important;
    color: #2c3e50 !important;
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 12px 15px !important;
    transition: all 0.3s ease !important;
}

.billing-form-container .form-control:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    color: #2c3e50 !important;
}

.billing-form-container .form-control::placeholder {
    color: #6c757d !important;
    font-style: italic;
}

.billing-form-container .form-text {
    font-size: 13px !important;
    color: #6c757d !important;
    margin-top: 5px !important;
}

.billing-form-container .text-danger {
    font-size: 14px !important;
    font-weight: 500 !important;
}

.billing-form-container .card-title {
    font-size: 20px !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
}

/* Ensure select options are visible */
.billing-form-container select.form-control option {
    color: #2c3e50 !important;
    background-color: #ffffff !important;
    font-size: 14px !important;
    padding: 8px !important;
}

/* Enhanced button styling */
.billing-form-container .btn {
    font-size: 15px !important;
    font-weight: 500 !important;
    padding: 10px 20px !important;
    border-radius: 6px !important;
}
</style>

<div class="billing-form-container">
<div class="row">
    <div class="col-md-8">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">Billing Information</h3>
            </div>
            <form method="post">
                {% csrf_token %}
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.client.id_for_label }}" class="form-label font-weight-bold text-dark" style="font-size: 16px; color: #333 !important;">
                                    <i class="fas fa-user text-primary"></i> Client *
                                </label>
                                {{ form.client }}
                                {% if form.client.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.client.errors %}
                                            <small><i class="fas fa-exclamation-circle"></i> {{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">Select the client for this billing</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.billing_month.id_for_label }}" class="form-label font-weight-bold text-dark" style="font-size: 16px; color: #333 !important;">
                                    <i class="fas fa-calendar text-primary"></i> Billing Month *
                                </label>
                                {{ form.billing_month }}
                                {% if form.billing_month.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.billing_month.errors %}
                                            <small><i class="fas fa-exclamation-circle"></i> {{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">Select the billing month (first day of the month)</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.previous_reading.id_for_label }}" class="form-label font-weight-bold text-dark" style="font-size: 16px; color: #333 !important;">
                                    <i class="fas fa-tachometer-alt text-warning"></i> Previous Reading (m³) *
                                </label>
                                {{ form.previous_reading }}
                                {% if form.previous_reading.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.previous_reading.errors %}
                                            <small><i class="fas fa-exclamation-circle"></i> {{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">Previous meter reading in cubic meters</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.current_reading.id_for_label }}" class="form-label font-weight-bold text-dark" style="font-size: 16px; color: #333 !important;">
                                    <i class="fas fa-tachometer-alt text-success"></i> Current Reading (m³) *
                                </label>
                                {{ form.current_reading }}
                                {% if form.current_reading.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.current_reading.errors %}
                                            <small><i class="fas fa-exclamation-circle"></i> {{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">Current meter reading in cubic meters</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.penalty.id_for_label }}" class="form-label font-weight-bold text-dark" style="font-size: 16px; color: #333 !important;">
                                    <i class="fas fa-exclamation-triangle text-danger"></i> Penalty Amount (₹)
                                </label>
                                {{ form.penalty }}
                                {% if form.penalty.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.penalty.errors %}
                                            <small><i class="fas fa-exclamation-circle"></i> {{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">Enter penalty amount for late payment (if any)</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.status.id_for_label }}" class="form-label font-weight-bold text-dark" style="font-size: 16px; color: #333 !important;">
                                    <i class="fas fa-info-circle text-info"></i> Status *
                                </label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.status.errors %}
                                            <small><i class="fas fa-exclamation-circle"></i> {{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">Select the payment status</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.due_date.id_for_label }}">Due Date *</label>
                                {{ form.due_date }}
                                {% if form.due_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.due_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.paid_date.id_for_label }}">Paid Date</label>
                                {{ form.paid_date }}
                                <small class="form-text text-muted">Only fill if status is 'Paid'</small>
                                {% if form.paid_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.paid_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.notes.id_for_label }}">Notes</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> {% if object %}Update{% else %}Create{% endif %} Billing
                    </button>
                    <a href="{% url 'billing:billing_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">Billing Calculation</h3>
            </div>
            <div class="card-body">
                <p><strong>How billing is calculated:</strong></p>
                <ol>
                    <li><strong>Consumption</strong> = Current Reading - Previous Reading</li>
                    <li><strong>Amount</strong> = Consumption × Client's Category Rate</li>
                    <li><strong>Total</strong> = Amount + Penalty</li>
                </ol>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Note:</strong> The system will automatically calculate consumption, amount, and total when you save the billing record.
                </div>
            </div>
        </div>
        
        <div class="card card-warning">
            <div class="card-header">
                <h3 class="card-title">Important Notes</h3>
            </div>
            <div class="card-body">
                <ul>
                    <li>Current reading must be greater than or equal to previous reading</li>
                    <li>Billing month should be the first day of the billing period</li>
                    <li>Due date is typically 30 days from billing month</li>
                    <li>Only set paid date when status is 'Paid'</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-calculate consumption when readings change
document.addEventListener('DOMContentLoaded', function() {
    const previousReading = document.getElementById('id_previous_reading');
    const currentReading = document.getElementById('id_current_reading');
    
    function calculateConsumption() {
        if (previousReading.value && currentReading.value) {
            const consumption = parseFloat(currentReading.value) - parseFloat(previousReading.value);
            if (consumption >= 0) {
                // You could display the calculated consumption here
                console.log('Consumption will be:', consumption, 'm³');
            }
        }
    }
    
    if (previousReading && currentReading) {
        previousReading.addEventListener('input', calculateConsumption);
        currentReading.addEventListener('input', calculateConsumption);
    }
});
</script>
</div> <!-- End billing-form-container -->
{% endblock %}
