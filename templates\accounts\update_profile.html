{% extends 'base.html' %}

{% block title %}Update Profile - Water Billing Management System{% endblock %}

{% block page_title %}Update Profile{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'accounts:profile' %}">Profile</a></li>
<li class="breadcrumb-item active">Update</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">Update Profile Information</h3>
            </div>
            <form method="post">
                {% csrf_token %}
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="first_name">First Name</label>
                                <input type="text" name="first_name" id="first_name" class="form-control" value="{{ user.first_name }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="last_name">Last Name</label>
                                <input type="text" name="last_name" id="last_name" class="form-control" value="{{ user.last_name }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="email" name="email" id="email" class="form-control" value="{{ user.email }}">
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="text" name="phone" id="phone" class="form-control" value="{{ user.phone }}">
                    </div>
                    
                    <div class="form-group">
                        <label for="address">Address</label>
                        <textarea name="address" id="address" class="form-control" rows="3">{{ user.address }}</textarea>
                    </div>
                    
                    <hr>
                    <h5>Change Password (Optional)</h5>
                    <p class="text-muted">Leave blank if you don't want to change your password.</p>
                    
                    <div class="form-group">
                        <label for="current_password">Current Password</label>
                        <input type="password" name="current_password" id="current_password" class="form-control">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="new_password">New Password</label>
                                <input type="password" name="new_password" id="new_password" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="confirm_password">Confirm New Password</label>
                                <input type="password" name="confirm_password" id="confirm_password" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Profile
                    </button>
                    <a href="{% url 'accounts:profile' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">Current Information</h3>
            </div>
            <div class="card-body">
                <p><strong>Username:</strong> {{ user.username }}</p>
                <p><strong>User Type:</strong> 
                    {% if user.user_type == 'admin' %}
                        <span class="badge badge-danger">Administrator</span>
                    {% else %}
                        <span class="badge badge-info">Staff</span>
                    {% endif %}
                </p>
                <p><strong>Last Login:</strong> 
                    {% if user.last_login %}
                        {{ user.last_login|date:"M d, Y H:i" }}
                    {% else %}
                        Never
                    {% endif %}
                </p>
                <p><strong>Member Since:</strong> {{ user.date_joined|date:"M d, Y" }}</p>
            </div>
        </div>
        
        <div class="card card-warning">
            <div class="card-header">
                <h3 class="card-title">Password Requirements</h3>
            </div>
            <div class="card-body">
                <p>When changing your password, make sure it:</p>
                <ul>
                    <li>Is at least 8 characters long</li>
                    <li>Contains both letters and numbers</li>
                    <li>Is not too similar to your username</li>
                    <li>Is not a commonly used password</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
