{% extends 'base.html' %}
{% load currency_filters %}

{% block title %}Monthly Billing Report - {{ month_name }} {{ year }}{% endblock %}

{% block page_title %}Monthly Billing Report{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'billing:billing_list' %}">Billing</a></li>
<li class="breadcrumb-item active">Monthly Report</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Monthly Billing Report - {{ month_name }} {{ year }}</h3>
                <div class="card-tools">
                    <!-- Export Dropdown -->
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-download"></i> Export Report
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="{% url 'billing:export_monthly_report_csv' %}?month={{ month }}&year={{ year }}">
                                <i class="fas fa-file-csv"></i> Download as CSV
                            </a>
                            <a class="dropdown-item" href="{% url 'billing:export_monthly_report_json' %}?month={{ month }}&year={{ year }}">
                                <i class="fas fa-file-code"></i> Download as JSON
                            </a>
                        </div>
                    </div>
                    <a href="{% url 'billing:print_monthly_report' %}?month={{ month }}&year={{ year }}" target="_blank" class="btn btn-info">
                        <i class="fas fa-print"></i> Print Report
                    </a>
                    <a href="{% url 'billing:billing_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Billing
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Month/Year Selector -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <form method="get" class="form-inline">
                            <div class="form-group mr-2">
                                <label for="month" class="mr-2">Month:</label>
                                <select name="month" id="month" class="form-control">
                                    <option value="1" {% if month == 1 %}selected{% endif %}>January</option>
                                    <option value="2" {% if month == 2 %}selected{% endif %}>February</option>
                                    <option value="3" {% if month == 3 %}selected{% endif %}>March</option>
                                    <option value="4" {% if month == 4 %}selected{% endif %}>April</option>
                                    <option value="5" {% if month == 5 %}selected{% endif %}>May</option>
                                    <option value="6" {% if month == 6 %}selected{% endif %}>June</option>
                                    <option value="7" {% if month == 7 %}selected{% endif %}>July</option>
                                    <option value="8" {% if month == 8 %}selected{% endif %}>August</option>
                                    <option value="9" {% if month == 9 %}selected{% endif %}>September</option>
                                    <option value="10" {% if month == 10 %}selected{% endif %}>October</option>
                                    <option value="11" {% if month == 11 %}selected{% endif %}>November</option>
                                    <option value="12" {% if month == 12 %}selected{% endif %}>December</option>
                                </select>
                            </div>
                            <div class="form-group mr-2">
                                <label for="year" class="mr-2">Year:</label>
                                <select name="year" id="year" class="form-control">
                                    <option value="2020" {% if year == 2020 %}selected{% endif %}>2020</option>
                                    <option value="2021" {% if year == 2021 %}selected{% endif %}>2021</option>
                                    <option value="2022" {% if year == 2022 %}selected{% endif %}>2022</option>
                                    <option value="2023" {% if year == 2023 %}selected{% endif %}>2023</option>
                                    <option value="2024" {% if year == 2024 %}selected{% endif %}>2024</option>
                                    <option value="2025" {% if year == 2025 %}selected{% endif %}>2025</option>
                                    <option value="2026" {% if year == 2026 %}selected{% endif %}>2026</option>
                                    <option value="2027" {% if year == 2027 %}selected{% endif %}>2027</option>
                                    <option value="2028" {% if year == 2028 %}selected{% endif %}>2028</option>
                                    <option value="2029" {% if year == 2029 %}selected{% endif %}>2029</option>
                                    <option value="2030" {% if year == 2030 %}selected{% endif %}>2030</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Generate Report
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Summary Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="info-box">
                            <span class="info-box-icon bg-info"><i class="fas fa-file-invoice"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total Bills</span>
                                <span class="info-box-number">{{ billing_count }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box">
                            <span class="info-box-icon bg-success"><i class="fas fa-tint"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total Consumption</span>
                                <span class="info-box-number">{{ total_consumption|floatformat:2 }} m³</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box">
                            <span class="info-box-icon bg-warning"><i class="fas fa-rupee-sign"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total Revenue</span>
                                <span class="info-box-number">{{ total_amount|currency }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box">
                            <span class="info-box-icon bg-danger"><i class="fas fa-calculator"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Average Bill</span>
                                <span class="info-box-number">
                                    {% if billing_count > 0 %}
                                        {{ total_amount|div:billing_count|currency }}
                                    {% else %}
                                        ₹0.00
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Billing Details Table -->
                {% if billings %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="bg-light">
                            <tr>
                                <th>Client Code</th>
                                <th>Client Name</th>
                                <th>Category</th>
                                <th>Meter Number</th>
                                <th>Previous Reading</th>
                                <th>Current Reading</th>
                                <th>Consumption</th>
                                <th>Rate</th>
                                <th>Amount</th>
                                <th>Penalty</th>
                                <th>Total</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for billing in billings %}
                            <tr>
                                <td><strong>{{ billing.client.code }}</strong></td>
                                <td>{{ billing.client.get_full_name }}</td>
                                <td>{{ billing.client.category.name }}</td>
                                <td>{{ billing.client.meter_number }}</td>
                                <td>{{ billing.previous_reading }} m³</td>
                                <td>{{ billing.current_reading }} m³</td>
                                <td><strong>{{ billing.consumption }} m³</strong></td>
                                <td>{{ billing.rate_per_cubic_meter|currency }}</td>
                                <td>{{ billing.amount|currency }}</td>
                                <td>
                                    {% if billing.penalty > 0 %}
                                        <span class="text-danger">{{ billing.penalty|currency }}</span>
                                    {% else %}
                                        ₹0.00
                                    {% endif %}
                                </td>
                                <td><strong>{{ billing.total_amount|currency }}</strong></td>
                                <td>
                                    {% if billing.status == 'paid' %}
                                        <span class="badge badge-success">Paid</span>
                                    {% elif billing.status == 'pending' %}
                                        <span class="badge badge-warning">Pending</span>
                                    {% else %}
                                        <span class="badge badge-danger">Overdue</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="bg-light">
                            <tr>
                                <th colspan="6">TOTALS</th>
                                <th>{{ total_consumption|floatformat:2 }} m³</th>
                                <th></th>
                                <th colspan="2"></th>
                                <th><strong>{{ total_amount|currency }}</strong></th>
                                <th>{{ billing_count }} bills</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle"></i>
                    <h4>No Billing Records Found</h4>
                    <p>There are no billing records for {{ month_name }} {{ year }}.</p>
                    <a href="{% url 'billing:billing_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create New Billing
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
// Custom filter for division
document.addEventListener('DOMContentLoaded', function() {
    // Add any JavaScript for report functionality here
});
</script>
{% endblock %}
