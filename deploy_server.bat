@echo off
echo ============================================
echo Deploying Water Billing Management System
echo ============================================
echo.

REM Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.11+ from https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo 1. Python version check...
python --version

echo.
echo 2. Creating virtual environment...
if exist venv (
    echo Virtual environment already exists, removing old one...
    rmdir /s /q venv
)

python -m venv venv
if errorlevel 1 (
    echo ERROR: Failed to create virtual environment
    pause
    exit /b 1
)

echo.
echo 3. Activating virtual environment...
call venv\Scripts\activate.bat

echo.
echo 4. Upgrading pip...
python -m pip install --upgrade pip --no-cache-dir

echo.
echo 5. Installing dependencies (this may take a few minutes)...
REM Install packages one by one to handle errors better
echo Installing Django...
pip install Django==5.2.4 --no-cache-dir
if errorlevel 1 (
    echo ERROR: Failed to install Django
    pause
    exit /b 1
)

echo Installing python-decouple...
pip install python-decouple==3.8 --no-cache-dir
if errorlevel 1 (
    echo ERROR: Failed to install python-decouple
    pause
    exit /b 1
)

echo Installing Pillow...
pip install Pillow==10.4.0 --no-cache-dir
if errorlevel 1 (
    echo ERROR: Failed to install Pillow
    pause
    exit /b 1
)

echo Installing requests...
pip install requests==2.32.3 --no-cache-dir
if errorlevel 1 (
    echo ERROR: Failed to install requests
    pause
    exit /b 1
)

echo Installing psycopg2-binary (PostgreSQL driver)...
REM Use precompiled wheel and disable cache
pip install --no-cache-dir --only-binary=psycopg2-binary psycopg2-binary==2.9.9
if errorlevel 1 (
    echo Trying alternative version...
    pip install --no-cache-dir --only-binary=psycopg2-binary psycopg2-binary==2.9.7
    if errorlevel 1 (
        echo Trying latest compatible version...
        pip install --no-cache-dir --only-binary=psycopg2-binary psycopg2-binary
        if errorlevel 1 (
            echo ERROR: Failed to install PostgreSQL driver
            echo Please install PostgreSQL client tools from:
            echo https://www.enterprisedb.com/downloads/postgres-postgresql-downloads
            echo Then install psycopg2 manually: pip install psycopg2-binary
            pause
            exit /b 1
        )
    )
)

echo Installing pywin32 for Windows service...
pip install pywin32==306 --no-cache-dir
if errorlevel 1 (
    echo WARNING: Failed to install pywin32 (Windows service won't work)
    echo Continuing with basic installation...
)

echo.
echo 6. Checking Django installation...
python -c "import django; print('Django version:', django.get_version())"
if errorlevel 1 (
    echo ERROR: Django installation verification failed
    pause
    exit /b 1
)

echo.
echo 7. Checking database connection...
python -c "from decouple import config; print('Configuration loaded successfully')"
if errorlevel 1 (
    echo ERROR: Configuration check failed
    pause
    exit /b 1
)

echo.
echo 8. Running database migrations...
python manage.py migrate
if errorlevel 1 (
    echo ERROR: Database migration failed
    echo Please check your Supabase connection settings in .env file
    pause
    exit /b 1
)

echo.
echo 9. Collecting static files...
python manage.py collectstatic --noinput
if errorlevel 1 (
    echo ERROR: Failed to collect static files
    pause
    exit /b 1
)

echo.
echo 10. Creating users...
echo Creating admin user...
python manage.py shell -c "from accounts.models import User; User.objects.filter(username='admin').exists() or User.objects.create_superuser('admin', '<EMAIL>', 'admin123', user_type='admin')"

echo Creating regular user...
python manage.py shell -c "from accounts.models import User; User.objects.filter(username='user').exists() or User.objects.create_user('user', '<EMAIL>', 'user123', user_type='customer')"

echo.
echo ============================================
echo Deployment completed successfully!
echo ============================================
echo.
echo Your Water Billing Management System is ready!
echo.
echo To start the server manually:
echo 1. Open Command Prompt as Administrator
echo 2. Navigate to: cd %CD%
echo 3. Activate environment: venv\Scripts\activate
echo 4. Start server: python manage.py runserver 0.0.0.0:8000
echo.
echo Access URLs:
echo - Local: http://localhost:8000
echo - Network: http://**************:8000
echo - Secondary: http://**************:8080
echo.
echo Login Credentials:
echo - Admin: Username=admin, Password=admin123
echo - Regular User: Username=user, Password=user123
echo.
echo To start the server now, press any key...
pause

echo.
echo Starting the development server...
echo Press Ctrl+C to stop the server
echo.
python manage.py runserver 0.0.0.0:8000