// Billing-specific JavaScript functions

$(document).ready(function() {
    
    // Auto-calculate consumption and amount in billing forms
    function initBillingCalculations() {
        const previousReading = $('#id_previous_reading');
        const currentReading = $('#id_current_reading');
        const clientSelect = $('#id_client');
        
        // Function to calculate and display consumption
        function calculateConsumption() {
            const prev = parseFloat(previousReading.val()) || 0;
            const curr = parseFloat(currentReading.val()) || 0;
            
            if (curr >= prev) {
                const consumption = curr - prev;
                
                // Display consumption if display element exists
                if ($('#consumption-display').length) {
                    $('#consumption-display').text(consumption.toFixed(2) + ' m³');
                }
                
                // Get rate from client category (if available)
                const selectedClient = clientSelect.find('option:selected');
                const rate = parseFloat(selectedClient.data('rate')) || 0;
                
                if (rate > 0) {
                    const amount = consumption * rate;
                    const penalty = parseFloat($('#id_penalty').val()) || 0;
                    const total = amount + penalty;
                    
                    // Display calculated amounts
                    if ($('#amount-display').length) {
                        $('#amount-display').text('₹' + amount.toFixed(2));
                    }
                    if ($('#total-display').length) {
                        $('#total-display').text('₹' + total.toFixed(2));
                    }
                }
                
                // Clear error styling
                currentReading.removeClass('is-invalid');
            } else if (curr < prev && curr > 0) {
                // Show error for invalid reading
                currentReading.addClass('is-invalid');
                if ($('#consumption-display').length) {
                    $('#consumption-display').text('Invalid reading');
                }
            }
        }
        
        // Bind events
        previousReading.on('input blur', calculateConsumption);
        currentReading.on('input blur', calculateConsumption);
        $('#id_penalty').on('input blur', calculateConsumption);
        
        // Initial calculation
        calculateConsumption();
    }
    
    // Initialize if we're on a billing form page
    if ($('#id_previous_reading').length) {
        initBillingCalculations();
    }
    
    // Status change handling
    $('#id_status').change(function() {
        const status = $(this).val();
        const paidDateField = $('#id_paid_date');
        const paidDateGroup = paidDateField.closest('.form-group');
        
        if (status === 'paid') {
            paidDateGroup.show();
            if (!paidDateField.val()) {
                // Set current date as default
                paidDateField.val(new Date().toISOString().split('T')[0]);
            }
        } else {
            paidDateGroup.hide();
            paidDateField.val('');
        }
    });
    
    // Initialize status field
    $('#id_status').trigger('change');
    
    // Due date auto-calculation
    $('#id_billing_month').change(function() {
        const billingDate = new Date($(this).val());
        if (billingDate) {
            // Add 30 days for due date
            const dueDate = new Date(billingDate);
            dueDate.setDate(dueDate.getDate() + 30);
            
            const dueDateField = $('#id_due_date');
            if (!dueDateField.val()) {
                dueDateField.val(dueDate.toISOString().split('T')[0]);
            }
        }
    });
    
    // Client selection handling
    $('#id_client').change(function() {
        const selectedOption = $(this).find('option:selected');
        const clientCode = selectedOption.data('code');
        const categoryName = selectedOption.data('category');
        const rate = selectedOption.data('rate');
        
        // Display client information
        if ($('#client-info').length) {
            $('#client-info').html(`
                <strong>Code:</strong> ${clientCode}<br>
                <strong>Category:</strong> ${categoryName}<br>
                <strong>Rate:</strong> $${rate}/m³
            `);
        }
        
        // Recalculate if readings are present
        if ($('#id_previous_reading').val() && $('#id_current_reading').val()) {
            $('#id_current_reading').trigger('input');
        }
    });
    
    // Form validation
    $('form').submit(function(e) {
        let isValid = true;
        const form = $(this);
        
        // Validate readings
        const prevReading = parseFloat($('#id_previous_reading').val()) || 0;
        const currReading = parseFloat($('#id_current_reading').val()) || 0;
        
        if (currReading < prevReading) {
            $('#id_current_reading').addClass('is-invalid');
            WaterBilling.showNotification('Current reading cannot be less than previous reading.', 'danger');
            isValid = false;
        }
        
        // Validate paid date for paid status
        const status = $('#id_status').val();
        const paidDate = $('#id_paid_date').val();
        
        if (status === 'paid' && !paidDate) {
            $('#id_paid_date').addClass('is-invalid');
            WaterBilling.showNotification('Paid date is required when status is "Paid".', 'danger');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
    
    // Print billing statement
    $('.print-billing').click(function(e) {
        e.preventDefault();
        
        // Hide non-essential elements
        $('.no-print').hide();
        
        // Print
        window.print();
        
        // Show elements again
        $('.no-print').show();
    });
    
    // Export billing data
    $('.export-billing').click(function(e) {
        e.preventDefault();
        
        const billingData = {
            client: $('#id_client option:selected').text(),
            billing_month: $('#id_billing_month').val(),
            previous_reading: $('#id_previous_reading').val(),
            current_reading: $('#id_current_reading').val(),
            consumption: parseFloat($('#id_current_reading').val()) - parseFloat($('#id_previous_reading').val()),
            penalty: $('#id_penalty').val(),
            status: $('#id_status option:selected').text(),
            due_date: $('#id_due_date').val()
        };
        
        // Convert to CSV
        const csv = Object.keys(billingData).map(key => 
            `"${key}","${billingData[key]}"`
        ).join('\n');
        
        // Download
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'billing_data.csv';
        a.click();
        window.URL.revokeObjectURL(url);
    });
    
});

// Billing utility functions
window.BillingUtils = {
    
    // Calculate consumption
    calculateConsumption: function(previousReading, currentReading) {
        return Math.max(0, currentReading - previousReading);
    },
    
    // Calculate billing amount
    calculateAmount: function(consumption, rate) {
        return consumption * rate;
    },
    
    // Calculate total with penalty
    calculateTotal: function(amount, penalty = 0) {
        return amount + penalty;
    },
    
    // Format meter reading
    formatReading: function(reading) {
        return parseFloat(reading).toFixed(2) + ' m³';
    },
    
    // Validate meter reading
    validateReading: function(previousReading, currentReading) {
        return currentReading >= previousReading;
    },
    
    // Get overdue days
    getOverdueDays: function(dueDate) {
        const today = new Date();
        const due = new Date(dueDate);
        const diffTime = today - due;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return Math.max(0, diffDays);
    },
    
    // Calculate penalty based on overdue days
    calculatePenalty: function(amount, overdueDays, penaltyRate = 0.05) {
        if (overdueDays <= 0) return 0;
        return amount * penaltyRate * Math.ceil(overdueDays / 30); // Monthly penalty
    }
    
};
