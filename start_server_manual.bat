@echo off
echo ============================================
echo Water Billing System - Manual Server Start
echo ============================================
echo.

REM Check if we're in the right directory
if not exist manage.py (
    echo ERROR: This script must be run from the Django project directory
    echo Expected location: C:\WebApps\WaterBilling\
    echo Current location: %CD%
    echo.
    pause
    exit /b 1
)

echo Project directory: %CD%
echo.

REM Check if virtual environment exists
if not exist venv (
    echo ERROR: Virtual environment not found!
    echo Please run deploy_supabase_windows.bat first
    echo.
    pause
    exit /b 1
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo.
echo Checking Django installation...
python -c "import django; print('Django version:', django.get_version())"
if errorlevel 1 (
    echo ERROR: Django not properly installed
    echo Please run deploy_supabase_windows.bat first
    pause
    exit /b 1
)

echo.
echo Testing database connection...
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'water_billing_system.settings')
import django
django.setup()
from django.db import connection
try:
    connection.ensure_connection()
    print('✅ Database connection: OK')
except Exception as e:
    print('❌ Database connection failed:', e)
    exit(1)
"

if errorlevel 1 (
    echo.
    echo Database connection failed!
    echo Please check your internet connection and Supabase settings
    echo.
    pause
    exit /b 1
)

echo.
echo Checking for admin user...
python manage.py shell -c "
from accounts.models import User
if User.objects.filter(username='admin').exists():
    print('✅ Admin user exists')
else:
    print('Creating admin user...')
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123', user_type='admin')
    print('✅ Admin user created')
"

echo.
echo Checking if port 8000 is available...
netstat -an | findstr :8000 >nul
if not errorlevel 1 (
    echo ⚠️ Port 8000 is already in use
    echo.
    echo What's using port 8000:
    netstat -ano | findstr :8000
    echo.
    echo Options:
    echo 1. Kill the process using port 8000
    echo 2. Use port 8080 instead
    echo 3. Use port 8001 instead
    echo.
    set /p port_choice="Enter port number to use (8000/8080/8001): "
    if "%port_choice%"=="" set port_choice=8080
) else (
    set port_choice=8000
)

echo.
echo ============================================
echo STARTING WATER BILLING MANAGEMENT SYSTEM
echo ============================================
echo.
echo 🌐 Your website will be accessible at:
echo - Local: http://localhost:%port_choice%
echo - Network: http://**************:%port_choice%
echo - Secondary: http://**************:%port_choice%
echo.
echo 🔐 Login credentials:
echo - Username: admin
echo - Password: admin123
echo.
echo 📊 Features:
echo ✅ Add/manage clients → Saves to Supabase
echo ✅ Add/manage users → Saves to Supabase
echo ✅ Generate billing reports
echo ✅ Dashboard with real-time statistics
echo.
echo 🛑 To stop the server: Press Ctrl+C
echo.
echo Starting server on port %port_choice%...
echo.

REM Start the Django development server
python manage.py runserver 0.0.0.0:%port_choice%

echo.
echo ============================================
echo SERVER STOPPED
echo ============================================
echo.
echo To restart the server:
echo 1. cd %CD%
echo 2. venv\Scripts\activate
echo 3. python manage.py runserver 0.0.0.0:%port_choice%
echo.
echo Or simply run this script again: start_server_manual.bat
echo.
pause
