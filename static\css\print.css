/* Print Styles for Water Billing Management System */

@media print {
    /* Hide non-essential elements */
    .no-print,
    .btn,
    .card-tools,
    .sidebar,
    .main-header,
    .main-footer,
    .content-header,
    .navbar,
    .breadcrumb,
    .alert,
    .pagination,
    .form-group,
    input,
    select,
    textarea,
    .btn-group {
        display: none !important;
    }
    
    /* Reset layout for print */
    body {
        font-family: 'Times New Roman', serif;
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: white;
        margin: 0;
        padding: 0;
    }
    
    .content-wrapper {
        margin-left: 0 !important;
        padding: 0 !important;
        background: white;
    }
    
    .content {
        padding: 0 !important;
        margin: 0 !important;
    }
    
    /* Card styling for print */
    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
        margin-bottom: 20pt;
        page-break-inside: avoid;
    }
    
    .card-header {
        background: #f0f0f0 !important;
        border-bottom: 2px solid #000 !important;
        padding: 10pt;
        font-weight: bold;
        font-size: 14pt;
    }
    
    .card-body {
        padding: 15pt;
    }
    
    /* Table styling for print */
    table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 15pt;
        font-size: 10pt;
    }
    
    table, th, td {
        border: 1px solid #000;
    }
    
    th {
        background-color: #f0f0f0 !important;
        font-weight: bold;
        padding: 8pt;
        text-align: left;
    }
    
    td {
        padding: 6pt;
        vertical-align: top;
    }
    
    /* Billing statement specific styles */
    .billing-header {
        text-align: center;
        margin-bottom: 20pt;
        border-bottom: 2px solid #000;
        padding-bottom: 10pt;
    }
    
    .billing-header h1 {
        font-size: 18pt;
        margin: 0;
        font-weight: bold;
    }
    
    .billing-header h2 {
        font-size: 14pt;
        margin: 5pt 0;
        font-weight: normal;
    }
    
    .client-info {
        margin-bottom: 20pt;
    }
    
    .client-info table {
        border: none;
    }
    
    .client-info td {
        border: none;
        padding: 3pt 10pt 3pt 0;
    }
    
    .client-info th {
        border: none;
        background: none !important;
        font-weight: bold;
        padding: 3pt 10pt 3pt 0;
        width: 30%;
    }
    
    /* Billing summary */
    .billing-summary {
        margin-top: 20pt;
        float: right;
        width: 50%;
    }
    
    .billing-summary table {
        border: 2px solid #000;
    }
    
    .billing-summary .total-row {
        background-color: #f0f0f0 !important;
        font-weight: bold;
        font-size: 12pt;
    }
    
    /* Page breaks */
    .page-break {
        page-break-before: always;
    }
    
    .page-break-inside {
        page-break-inside: avoid;
    }
    
    /* Footer for billing statements */
    .billing-footer {
        margin-top: 30pt;
        border-top: 1px solid #000;
        padding-top: 10pt;
        font-size: 10pt;
        text-align: center;
    }
    
    /* Report specific styles */
    .report-title {
        text-align: center;
        font-size: 16pt;
        font-weight: bold;
        margin-bottom: 20pt;
        border-bottom: 2px solid #000;
        padding-bottom: 10pt;
    }
    
    .report-summary {
        margin-bottom: 20pt;
        border: 1px solid #000;
        padding: 10pt;
    }
    
    .report-summary h3 {
        margin: 0 0 10pt 0;
        font-size: 12pt;
    }
    
    .summary-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10pt;
    }
    
    .summary-item {
        border: 1px solid #ccc;
        padding: 8pt;
        text-align: center;
    }
    
    .summary-label {
        font-weight: bold;
        font-size: 10pt;
    }
    
    .summary-value {
        font-size: 12pt;
        margin-top: 5pt;
    }
    
    /* Badge styles for print */
    .badge {
        border: 1px solid #000;
        padding: 2pt 6pt;
        font-size: 9pt;
        font-weight: bold;
    }
    
    .badge-success {
        background: #e8f5e8 !important;
        color: #000 !important;
    }
    
    .badge-warning {
        background: #fff3cd !important;
        color: #000 !important;
    }
    
    .badge-danger {
        background: #f8d7da !important;
        color: #000 !important;
    }
    
    .badge-info {
        background: #d1ecf1 !important;
        color: #000 !important;
    }
    
    /* Text utilities for print */
    .text-right {
        text-align: right;
    }
    
    .text-center {
        text-align: center;
    }
    
    .text-left {
        text-align: left;
    }
    
    .font-weight-bold {
        font-weight: bold;
    }
    
    /* Spacing utilities */
    .mb-3 {
        margin-bottom: 15pt;
    }
    
    .mt-3 {
        margin-top: 15pt;
    }
    
    .p-3 {
        padding: 15pt;
    }
    
    /* Print-specific classes */
    .print-only {
        display: block !important;
    }
    
    .print-hide {
        display: none !important;
    }
    
    /* Company letterhead */
    .letterhead {
        text-align: center;
        margin-bottom: 30pt;
        border-bottom: 3px solid #000;
        padding-bottom: 15pt;
    }
    
    .letterhead h1 {
        font-size: 20pt;
        margin: 0;
        font-weight: bold;
    }
    
    .letterhead .company-info {
        font-size: 10pt;
        margin-top: 10pt;
        line-height: 1.2;
    }
    
    /* Page numbering */
    @page {
        margin: 1in;
        @bottom-right {
            content: "Page " counter(page) " of " counter(pages);
            font-size: 9pt;
        }
    }
    
    /* Avoid breaking these elements */
    .keep-together {
        page-break-inside: avoid;
    }
    
    /* Force page break before these elements */
    .new-page {
        page-break-before: always;
    }
}
