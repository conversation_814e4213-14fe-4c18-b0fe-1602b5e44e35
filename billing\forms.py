from django import forms
from .models import Billing
from clients.models import Client

class BillingForm(forms.ModelForm):
    class Meta:
        model = Billing
        fields = ['client', 'billing_month', 'previous_reading', 'current_reading', 'penalty', 'status', 'due_date', 'paid_date', 'notes']
        widgets = {
            'client': forms.Select(attrs={
                'class': 'form-control form-control-lg',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'billing_month': forms.DateInput(attrs={
                'class': 'form-control form-control-lg',
                'type': 'date',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'previous_reading': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'step': '0.01',
                'placeholder': 'Enter previous meter reading',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'current_reading': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'step': '0.01',
                'placeholder': 'Enter current meter reading',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'penalty': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'step': '0.01',
                'placeholder': 'Enter penalty amount (if any)',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'status': forms.Select(attrs={
                'class': 'form-control form-control-lg',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'due_date': forms.DateInput(attrs={
                'class': 'form-control form-control-lg',
                'type': 'date',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'paid_date': forms.DateInput(attrs={
                'class': 'form-control form-control-lg',
                'type': 'date',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control form-control-lg',
                'rows': 3,
                'placeholder': 'Enter any additional notes or comments',
                'style': 'font-size: 14px; color: #333; font-weight: 500;'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['client'].queryset = Client.objects.filter(status=True).order_by('lastname', 'firstname')
        self.fields['paid_date'].required = False
        
    def clean(self):
        cleaned_data = super().clean()
        previous_reading = cleaned_data.get('previous_reading')
        current_reading = cleaned_data.get('current_reading')
        
        if previous_reading and current_reading:
            if current_reading < previous_reading:
                raise forms.ValidationError('Current reading cannot be less than previous reading.')
        
        return cleaned_data
