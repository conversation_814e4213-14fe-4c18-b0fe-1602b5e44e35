// Water Billing Management System - Custom JavaScript

$(document).ready(function() {
    
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Initialize popovers
    $('[data-toggle="popover"]').popover();
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
    
    // Confirm delete actions
    $('.btn-danger[href*="delete"]').click(function(e) {
        if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
            e.preventDefault();
        }
    });
    
    // Form validation enhancements
    $('form').submit(function() {
        var submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        submitBtn.html('<span class="loading"></span> Processing...');
        
        // Re-enable after 3 seconds to prevent permanent disable
        setTimeout(function() {
            submitBtn.prop('disabled', false);
            submitBtn.html(submitBtn.data('original-text') || 'Submit');
        }, 3000);
    });
    
    // Store original button text
    $('button[type="submit"]').each(function() {
        $(this).data('original-text', $(this).html());
    });
    
    // Number formatting for currency inputs
    $('input[type="number"]').on('blur', function() {
        var value = parseFloat($(this).val());
        if (!isNaN(value)) {
            $(this).val(value.toFixed(2));
        }
    });
    
    // Auto-calculate consumption in billing forms
    $('#id_previous_reading, #id_current_reading').on('input', function() {
        calculateConsumption();
    });
    
    function calculateConsumption() {
        var previousReading = parseFloat($('#id_previous_reading').val()) || 0;
        var currentReading = parseFloat($('#id_current_reading').val()) || 0;
        
        if (currentReading >= previousReading) {
            var consumption = currentReading - previousReading;
            
            // Display consumption if there's a display element
            if ($('#consumption-display').length) {
                $('#consumption-display').text(consumption.toFixed(2) + ' m³');
            }
            
            // Calculate estimated amount if rate is available
            var rate = parseFloat($('#rate-per-cubic-meter').text()) || 0;
            if (rate > 0) {
                var estimatedAmount = consumption * rate;
                if ($('#estimated-amount').length) {
                    $('#estimated-amount').text('₹' + estimatedAmount.toFixed(2));
                }
            }
        }
    }
    
    // Search functionality enhancement
    $('.search-input').on('keyup', function() {
        var searchTerm = $(this).val().toLowerCase();
        var targetTable = $(this).data('target') || 'table tbody tr';
        
        $(targetTable).each(function() {
            var rowText = $(this).text().toLowerCase();
            if (rowText.indexOf(searchTerm) === -1) {
                $(this).hide();
            } else {
                $(this).show();
            }
        });
    });
    
    // Print functionality
    $('.print-btn').click(function() {
        window.print();
    });
    
    // Export to CSV functionality
    $('.export-csv').click(function() {
        var table = $(this).data('table') || 'table';
        exportTableToCSV($(table), 'water_billing_export.csv');
    });
    
    function exportTableToCSV($table, filename) {
        var $rows = $table.find('tr:has(td)'),
            tmpColDelim = String.fromCharCode(11),
            tmpRowDelim = String.fromCharCode(0),
            colDelim = '","',
            rowDelim = '"\r\n"',
            csv = '"' + $rows.map(function (i, row) {
                var $row = $(row),
                    $cols = $row.find('td');
                return $cols.map(function (j, col) {
                    var $col = $(col),
                        text = $col.text();
                    return text.replace(/"/g, '""');
                }).get().join(tmpColDelim);
            }).get().join(tmpRowDelim)
                .split(tmpRowDelim).join(rowDelim)
                .split(tmpColDelim).join(colDelim) + '"';
        
        // Add header row
        var $headerRow = $table.find('thead tr:first');
        if ($headerRow.length) {
            var headers = $headerRow.find('th').map(function() {
                return $(this).text();
            }).get().join(colDelim);
            csv = '"' + headers + '"\r\n' + csv;
        }
        
        downloadCSV(csv, filename);
    }
    
    function downloadCSV(csv, filename) {
        var csvFile = new Blob([csv], {type: "text/csv"});
        var downloadLink = document.createElement("a");
        downloadLink.download = filename;
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = "none";
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
    }
    
    // Status badge color updates
    $('.status-select').change(function() {
        var status = $(this).val();
        var badge = $(this).closest('tr').find('.status-badge');
        
        badge.removeClass('badge-success badge-warning badge-danger');
        
        switch(status) {
            case 'paid':
                badge.addClass('badge-success').text('Paid');
                break;
            case 'pending':
                badge.addClass('badge-warning').text('Pending');
                break;
            case 'overdue':
                badge.addClass('badge-danger').text('Overdue');
                break;
        }
    });
    
    // Auto-refresh dashboard data every 5 minutes
    if (window.location.pathname === '/dashboard/') {
        setInterval(function() {
            location.reload();
        }, 300000); // 5 minutes
    }
    
    // Sidebar menu state persistence
    $('.nav-sidebar .nav-link').click(function() {
        localStorage.setItem('activeMenu', $(this).attr('href'));
    });
    
    // Restore active menu state
    var activeMenu = localStorage.getItem('activeMenu');
    if (activeMenu) {
        $('.nav-sidebar .nav-link[href="' + activeMenu + '"]').addClass('active');
    }
    
    // Form field validation
    $('input[required]').on('blur', function() {
        if ($(this).val() === '') {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid').addClass('is-valid');
        }
    });
    
    // Email validation
    $('input[type="email"]').on('blur', function() {
        var email = $(this).val();
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            $(this).addClass('is-invalid');
        } else if (email) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        }
    });
    
    // Phone number formatting
    $('input[type="tel"], input[name*="phone"], input[name*="contact"]').on('input', function() {
        var value = $(this).val().replace(/\D/g, '');
        var formattedValue = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        $(this).val(formattedValue);
    });
    
    // Meter number validation
    $('input[name*="meter"]').on('input', function() {
        $(this).val($(this).val().toUpperCase());
    });
    
    // Client code validation
    $('input[name*="code"]').on('input', function() {
        $(this).val($(this).val().toUpperCase());
    });
    
    // Date picker enhancement
    $('input[type="date"]').each(function() {
        if (!$(this).val()) {
            $(this).val(new Date().toISOString().split('T')[0]);
        }
    });
    
    // Loading overlay for AJAX requests
    $(document).ajaxStart(function() {
        $('body').append('<div class="loading-overlay"><div class="loading"></div></div>');
    });
    
    $(document).ajaxStop(function() {
        $('.loading-overlay').remove();
    });
    
    // Responsive table wrapper
    $('table').wrap('<div class="table-responsive"></div>');
    
    // Auto-save form data to localStorage
    $('form input, form textarea, form select').on('change', function() {
        var formId = $(this).closest('form').attr('id');
        if (formId) {
            var formData = $(this).closest('form').serialize();
            localStorage.setItem('form_' + formId, formData);
        }
    });
    
    // Restore form data from localStorage
    $('form[id]').each(function() {
        var formId = $(this).attr('id');
        var savedData = localStorage.getItem('form_' + formId);
        if (savedData) {
            // Parse and restore form data
            var params = new URLSearchParams(savedData);
            var form = this;
            params.forEach(function(value, key) {
                var field = $(form).find('[name="' + key + '"]');
                if (field.length && !field.val()) {
                    field.val(value);
                }
            });
        }
    });
    
    // Clear saved form data on successful submission
    $('form').on('submit', function() {
        var formId = $(this).attr('id');
        if (formId) {
            localStorage.removeItem('form_' + formId);
        }
    });
    
});

// Global utility functions
window.WaterBilling = {
    
    // Format currency
    formatCurrency: function(amount) {
        return '₹' + parseFloat(amount).toFixed(2);
    },
    
    // Format consumption
    formatConsumption: function(consumption) {
        return parseFloat(consumption).toFixed(2) + ' m³';
    },
    
    // Calculate billing amount
    calculateBilling: function(consumption, rate, penalty = 0) {
        var amount = consumption * rate;
        return amount + penalty;
    },
    
    // Show notification
    showNotification: function(message, type = 'info') {
        var alertClass = 'alert-' + type;
        var alert = $('<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                     message +
                     '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                     '</div>');
        
        $('.content').prepend(alert);
        
        setTimeout(function() {
            alert.fadeOut();
        }, 5000);
    },
    
    // Confirm action
    confirmAction: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }
    
};
