{% extends 'base.html' %}

{% block title %}User Management - Water Billing Management System{% endblock %}

{% block page_title %}User Management{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item active">User Management</li>
{% endblock %}

{% block content %}
<!-- User Statistics -->
<div class="row mb-3">
    <div class="col-md-4">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3>{{ user_type_stats.admin }}</h3>
                <p>Admin Users</p>
            </div>
            <div class="icon">
                <i class="fas fa-crown"></i>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ user_type_stats.staff }}</h3>
                <p>Staff Users</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-tie"></i>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="small-box bg-secondary">
            <div class="inner">
                <h3>{{ user_type_stats.user }}</h3>
                <p>Regular Users</p>
            </div>
            <div class="icon">
                <i class="fas fa-user"></i>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">System Users</h3>
                <div class="card-tools">
                    <a href="{% url 'accounts:user_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New User
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Full Name</th>
                                <th>Email</th>
                                <th>User Type</th>
                                <th>Status</th>
                                <th>Last Login</th>
                                <th>Date Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td><strong>{{ user.username }}</strong></td>
                                <td>{{ user.get_full_name|default:"-" }}</td>
                                <td>{{ user.email|default:"-" }}</td>
                                <td>
                                    {% if user.user_type == 'admin' %}
                                        <span class="badge badge-danger">
                                            <i class="fas fa-crown"></i> Admin
                                        </span>
                                    {% elif user.user_type == 'staff' %}
                                        <span class="badge badge-info">
                                            <i class="fas fa-user-tie"></i> Staff
                                        </span>
                                    {% else %}
                                        <span class="badge badge-secondary">
                                            <i class="fas fa-user"></i> User
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_active %}
                                        <span class="badge badge-success">Active</span>
                                    {% else %}
                                        <span class="badge badge-secondary">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.last_login %}
                                        {{ user.last_login|date:"M d, Y H:i" }}
                                    {% else %}
                                        <span class="text-muted">Never</span>
                                    {% endif %}
                                </td>
                                <td>{{ user.date_joined|date:"M d, Y" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'accounts:user_update' user.pk %}" class="btn btn-sm btn-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if user != request.user %}
                                        <a href="{% url 'accounts:user_delete' user.pk %}" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this user?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="8" class="text-center">No users found.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <div class="row">
                    <div class="col-sm-12 col-md-5">
                        <div class="dataTables_info">
                            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-7">
                        <div class="dataTables_paginate paging_simple_numbers">
                            <ul class="pagination">
                                {% if page_obj.has_previous %}
                                    <li class="paginate_button page-item previous">
                                        <a href="?page={{ page_obj.previous_page_number }}" class="page-link">Previous</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="paginate_button page-item active">
                                            <a href="#" class="page-link">{{ num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="paginate_button page-item">
                                            <a href="?page={{ num }}" class="page-link">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="paginate_button page-item next">
                                        <a href="?page={{ page_obj.next_page_number }}" class="page-link">Next</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
