{% extends 'base.html' %}

{% block title %}Delete Category - Water Billing Management System{% endblock %}

{% block page_title %}Delete Category{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'categories:category_list' %}">Categories</a></li>
<li class="breadcrumb-item active">Delete</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card card-danger">
            <div class="card-header">
                <h3 class="card-title">Confirm Deletion</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                
                <p>Are you sure you want to delete the category <strong>"{{ object.name }}"</strong>?</p>
                
                <div class="card">
                    <div class="card-body">
                        <h5>Category Details:</h5>
                        <ul>
                            <li><strong>Name:</strong> {{ object.name }}</li>
                            <li><strong>Price per m³:</strong> ${{ object.price_per_cubic_meter|floatformat:2 }}</li>
                            <li><strong>Status:</strong> 
                                {% if object.status %}
                                    <span class="badge badge-success">Active</span>
                                {% else %}
                                    <span class="badge badge-danger">Inactive</span>
                                {% endif %}
                            </li>
                            <li><strong>Clients using this category:</strong> {{ object.clients.count }}</li>
                        </ul>
                    </div>
                </div>
                
                {% if object.clients.count > 0 %}
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <strong>Note:</strong> This category has {{ object.clients.count }} client(s) associated with it. 
                    Deleting this category may affect billing calculations for these clients.
                </div>
                {% endif %}
            </div>
            
            <div class="card-footer">
                <form method="post">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Yes, Delete Category
                    </button>
                    <a href="{% url 'categories:category_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
