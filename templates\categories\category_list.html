{% extends 'base.html' %}
{% load currency_filters %}

{% block title %}Categories - Water Billing Management System{% endblock %}

{% block page_title %}Categories{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item active">Categories</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Category Management</h3>
                <div class="card-tools">
                    <a href="{% url 'categories:category_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Category
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Price per m³</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for category in categories %}
                            <tr>
                                <td><strong>{{ category.name }}</strong></td>
                                <td>{{ category.description|truncatewords:10|default:"No description" }}</td>
                                <td>{{ category.price_per_cubic_meter|currency }}</td>
                                <td>
                                    {% if category.status %}
                                        <span class="badge badge-success">Active</span>
                                    {% else %}
                                        <span class="badge badge-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>{{ category.created_at|date:"M d, Y" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'categories:category_detail' category.pk %}" class="btn btn-sm btn-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'categories:category_update' category.pk %}" class="btn btn-sm btn-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'categories:category_delete' category.pk %}" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this category?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center">No categories found.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <div class="row">
                    <div class="col-sm-12 col-md-5">
                        <div class="dataTables_info">
                            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-7">
                        <div class="dataTables_paginate paging_simple_numbers">
                            <ul class="pagination">
                                {% if page_obj.has_previous %}
                                    <li class="paginate_button page-item previous">
                                        <a href="?page={{ page_obj.previous_page_number }}" class="page-link">Previous</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="paginate_button page-item active">
                                            <a href="#" class="page-link">{{ num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="paginate_button page-item">
                                            <a href="?page={{ num }}" class="page-link">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="paginate_button page-item next">
                                        <a href="?page={{ page_obj.next_page_number }}" class="page-link">Next</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
