{% extends 'base.html' %}
{% load currency_filters %}

{% block title %}{{ category.name }} - Category Details{% endblock %}

{% block page_title %}Category Details{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'categories:category_list' %}">Categories</a></li>
<li class="breadcrumb-item active">{{ category.name }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">{{ category.name }}</h3>
                <div class="card-tools">
                    <a href="{% url 'categories:category_update' category.pk %}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{% url 'categories:category_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <th width="30%">Category Name:</th>
                        <td>{{ category.name }}</td>
                    </tr>
                    <tr>
                        <th>Description:</th>
                        <td>{{ category.description|default:"No description provided"|linebreaks }}</td>
                    </tr>
                    <tr>
                        <th>Price per Cubic Meter:</th>
                        <td><strong>{{ category.price_per_cubic_meter|currency }}</strong></td>
                    </tr>
                    <tr>
                        <th>Status:</th>
                        <td>
                            {% if category.status %}
                                <span class="badge badge-success">Active</span>
                            {% else %}
                                <span class="badge badge-danger">Inactive</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>Created:</th>
                        <td>{{ category.created_at|date:"M d, Y H:i" }}</td>
                    </tr>
                    <tr>
                        <th>Last Updated:</th>
                        <td>{{ category.updated_at|date:"M d, Y H:i" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">Clients in this Category</h3>
            </div>
            <div class="card-body">
                <div class="info-box">
                    <span class="info-box-icon bg-info"><i class="fas fa-users"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Clients</span>
                        <span class="info-box-number">{{ client_count }}</span>
                    </div>
                </div>
                
                {% if clients %}
                <h5>Recent Clients:</h5>
                <ul class="list-group list-group-flush">
                    {% for client in clients|slice:":5" %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>{{ client.code }}</strong><br>
                            <small>{{ client.get_full_name }}</small>
                        </div>
                        <a href="{% url 'clients:client_detail' client.pk %}" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-eye"></i>
                        </a>
                    </li>
                    {% endfor %}
                </ul>
                
                {% if client_count > 5 %}
                <div class="mt-2">
                    <a href="{% url 'clients:client_list' %}?category={{ category.pk }}" class="btn btn-sm btn-info">
                        View All Clients
                    </a>
                </div>
                {% endif %}
                {% else %}
                <p class="text-muted">No clients in this category yet.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
