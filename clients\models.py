from django.db import models
from categories.models import Category

class Client(models.Model):
    code = models.Char<PERSON>ield(max_length=50, unique=True, help_text="Unique client code")
    firstname = models.CharField(max_length=100)
    lastname = models.Char<PERSON>ield(max_length=100)
    middlename = models.CharField(max_length=100, blank=True, null=True)
    contact = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    address = models.TextField()
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='clients')
    meter_number = models.Char<PERSON>ield(max_length=100, unique=True)
    status = models.BooleanField(default=True, help_text="Active/Inactive")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.code} - {self.get_full_name()}"

    def get_full_name(self):
        if self.middlename:
            return f"{self.firstname} {self.middlename} {self.lastname}"
        return f"{self.firstname} {self.lastname}"

    @property
    def full_name(self):
        return self.get_full_name()

    class Meta:
        db_table = 'wbms_clients'
        verbose_name = 'Client'
        verbose_name_plural = 'Clients'
        ordering = ['lastname', 'firstname']
