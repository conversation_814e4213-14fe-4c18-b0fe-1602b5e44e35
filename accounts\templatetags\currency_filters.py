from django import template
from django.conf import settings
import locale

register = template.Library()

@register.filter
def currency(value):
    """
    Format a number as Indian Rupees currency
    """
    try:
        # Convert to float if it's not already
        if value is None:
            return "₹0.00"
        
        amount = float(value)
        
        # Format with Indian number system (lakhs, crores)
        if amount >= 10000000:  # 1 crore
            crores = amount / 10000000
            return f"₹{crores:.2f} Cr"
        elif amount >= 100000:  # 1 lakh
            lakhs = amount / 100000
            return f"₹{lakhs:.2f} L"
        else:
            # Format with commas for thousands
            return f"₹{amount:,.2f}"
    except (ValueError, TypeError):
        return "₹0.00"

@register.filter
def rupees(value):
    """
    Simple rupees formatting
    """
    try:
        if value is None:
            return "₹0.00"
        amount = float(value)
        return f"₹{amount:.2f}"
    except (ValueError, TypeError):
        return "₹0.00"

@register.filter
def indian_currency(value):
    """
    Format currency in Indian style with proper comma placement
    """
    try:
        if value is None:
            return "₹0.00"
        
        amount = float(value)
        
        # Convert to string and split by decimal
        amount_str = f"{amount:.2f}"
        integer_part, decimal_part = amount_str.split('.')
        
        # Add commas in Indian style (last 3 digits, then every 2 digits)
        if len(integer_part) > 3:
            # Reverse the string for easier processing
            reversed_int = integer_part[::-1]
            
            # Add comma after first 3 digits, then every 2 digits
            formatted = ""
            for i, digit in enumerate(reversed_int):
                if i == 3:
                    formatted += ","
                elif i > 3 and (i - 3) % 2 == 0:
                    formatted += ","
                formatted += digit
            
            # Reverse back to get the correct order
            integer_part = formatted[::-1]
        
        return f"₹{integer_part}.{decimal_part}"
    except (ValueError, TypeError):
        return "₹0.00"

@register.filter
def format_consumption(value):
    """
    Format water consumption with proper units
    """
    try:
        if value is None:
            return "0.00 m³"
        consumption = float(value)
        return f"{consumption:.2f} m³"
    except (ValueError, TypeError):
        return "0.00 m³"

@register.filter
def div(value, arg):
    """
    Divide value by arg
    """
    try:
        return float(value) / float(arg)
    except (ValueError, TypeError, ZeroDivisionError):
        return 0
