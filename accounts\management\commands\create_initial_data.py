from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import SystemInfo
from categories.models import Category
from clients.models import Client
from billing.models import Billing
from datetime import datetime, timedelta
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = 'Create initial data for the water billing system'

    def handle(self, *args, **options):
        self.stdout.write('Creating initial data...')
        
        # Create admin user
        if not User.objects.filter(username='admin').exists():
            admin_user = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='System',
                last_name='Administrator',
                user_type='admin'
            )
            self.stdout.write(self.style.SUCCESS('Admin user created: admin/admin123'))
        
        # Create staff user
        if not User.objects.filter(username='staff').exists():
            staff_user = User.objects.create_user(
                username='staff',
                email='<EMAIL>',
                password='staff123',
                first_name='Staff',
                last_name='User',
                user_type='staff'
            )
            self.stdout.write(self.style.SUCCESS('Staff user created: staff/staff123'))

        # Create regular user
        if not User.objects.filter(username='user').exists():
            regular_user = User.objects.create_user(
                username='user',
                email='<EMAIL>',
                password='user123',
                first_name='Regular',
                last_name='User',
                user_type='user'
            )
            self.stdout.write(self.style.SUCCESS('Regular user created: user/user123'))
        
        # Create system info
        system_info, created = SystemInfo.objects.get_or_create(
            pk=1,
            defaults={
                'name': 'Water Billing Management System',
                'short_name': 'WBMS',
                'address': '123 Water Street, City, State 12345',
                'phone': '+****************',
                'email': '<EMAIL>',
                'website': 'https://waterbilling.com'
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('System information created'))
        
        # Create categories
        categories_data = [
            {'name': 'Residential', 'description': 'For residential properties', 'price': '2.50'},
            {'name': 'Commercial', 'description': 'For commercial properties', 'price': '3.75'},
            {'name': 'Industrial', 'description': 'For industrial properties', 'price': '5.00'},
        ]
        
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={
                    'description': cat_data['description'],
                    'price_per_cubic_meter': Decimal(cat_data['price']),
                    'status': True
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Category created: {category.name}'))
        
        # Create sample clients
        residential_category = Category.objects.get(name='Residential')
        commercial_category = Category.objects.get(name='Commercial')
        
        clients_data = [
            {
                'code': 'RES001',
                'firstname': 'John',
                'lastname': 'Doe',
                'email': '<EMAIL>',
                'contact': '555-0101',
                'address': '123 Main St, Anytown, ST 12345',
                'category': residential_category,
                'meter_number': 'MTR001'
            },
            {
                'code': 'RES002',
                'firstname': 'Jane',
                'lastname': 'Smith',
                'email': '<EMAIL>',
                'contact': '555-0102',
                'address': '456 Oak Ave, Anytown, ST 12345',
                'category': residential_category,
                'meter_number': 'MTR002'
            },
            {
                'code': 'COM001',
                'firstname': 'Business',
                'lastname': 'Owner',
                'email': '<EMAIL>',
                'contact': '555-0201',
                'address': '789 Business Blvd, Anytown, ST 12345',
                'category': commercial_category,
                'meter_number': 'MTR003'
            }
        ]
        
        for client_data in clients_data:
            client, created = Client.objects.get_or_create(
                code=client_data['code'],
                defaults=client_data
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Client created: {client.code} - {client.get_full_name()}'))
        
        # Create sample billings
        admin_user = User.objects.get(username='admin')
        current_date = datetime.now().date()
        
        for client in Client.objects.all():
            # Create billing for current month
            billing, created = Billing.objects.get_or_create(
                client=client,
                billing_month=current_date.replace(day=1),
                defaults={
                    'previous_reading': Decimal('100.00'),
                    'current_reading': Decimal('125.50'),
                    'rate_per_cubic_meter': client.category.price_per_cubic_meter,
                    'penalty': Decimal('0.00'),
                    'status': 'pending',
                    'due_date': current_date + timedelta(days=30),
                    'created_by': admin_user
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Billing created for {client.get_full_name()}'))
        
        self.stdout.write(self.style.SUCCESS('Initial data creation completed!'))
        self.stdout.write('You can now login with:')
        self.stdout.write('Admin: admin/admin123')
        self.stdout.write('Staff: staff/staff123')
