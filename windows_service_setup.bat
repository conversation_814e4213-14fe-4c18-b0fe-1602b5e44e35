@echo off
echo ============================================
echo Water Billing System - Windows Service Setup
echo ============================================
echo.
echo This will set up your app to run as a Windows Service
echo so it starts automatically when the server boots.
echo.

REM Check if running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo ERROR: This script must be run as Administrator!
    echo.
    echo Right-click on this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo Running as Administrator: OK
echo.

REM Check if virtual environment exists
if not exist venv (
    echo ERROR: Virtual environment not found!
    echo Please run deploy_supabase_windows.bat first
    pause
    exit /b 1
)

echo Virtual environment found: OK
echo.

echo Step 1: Installing Windows service dependencies...
call venv\Scripts\activate.bat
pip install pywin32==306
if errorlevel 1 (
    echo ERROR: Failed to install Windows service support
    pause
    exit /b 1
)

echo.
echo Step 2: Creating Windows service script...

REM Create the service script
echo import win32serviceutil > django_service.py
echo import win32service >> django_service.py
echo import win32event >> django_service.py
echo import socket >> django_service.py
echo import subprocess >> django_service.py
echo import os >> django_service.py
echo import sys >> django_service.py
echo import time >> django_service.py
echo. >> django_service.py
echo class WaterBillingService(win32serviceutil.ServiceFramework): >> django_service.py
echo     _svc_name_ = "WaterBillingSystem" >> django_service.py
echo     _svc_display_name_ = "Water Billing Management System" >> django_service.py
echo     _svc_description_ = "Django Water Billing Management System with Supabase integration" >> django_service.py
echo. >> django_service.py
echo     def __init__(self, args): >> django_service.py
echo         win32serviceutil.ServiceFramework.__init__(self, args) >> django_service.py
echo         self.hWaitStop = win32event.CreateEvent(None, 0, 0, None) >> django_service.py
echo         socket.setdefaulttimeout(60) >> django_service.py
echo         self.process = None >> django_service.py
echo. >> django_service.py
echo     def SvcStop(self): >> django_service.py
echo         self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING) >> django_service.py
echo         win32event.SetEvent(self.hWaitStop) >> django_service.py
echo         if self.process: >> django_service.py
echo             self.process.terminate() >> django_service.py
echo             self.process.wait() >> django_service.py
echo. >> django_service.py
echo     def SvcDoRun(self): >> django_service.py
echo         # Change to the app directory >> django_service.py
echo         os.chdir(r'%CD%') >> django_service.py
echo. >> django_service.py
echo         # Start Django server >> django_service.py
echo         cmd = [ >> django_service.py
echo             r'%CD%\venv\Scripts\python.exe', >> django_service.py
echo             'manage.py', >> django_service.py
echo             'runserver', >> django_service.py
echo             '0.0.0.0:8000', >> django_service.py
echo             '--noreload' >> django_service.py
echo         ] >> django_service.py
echo. >> django_service.py
echo         # Start the process >> django_service.py
echo         self.process = subprocess.Popen(cmd, >> django_service.py
echo             stdout=subprocess.PIPE, >> django_service.py
echo             stderr=subprocess.PIPE, >> django_service.py
echo             cwd=r'%CD%') >> django_service.py
echo. >> django_service.py
echo         # Wait for stop signal >> django_service.py
echo         win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE) >> django_service.py
echo. >> django_service.py
echo if __name__ == '__main__': >> django_service.py
echo     win32serviceutil.HandleCommandLine(WaterBillingService) >> django_service.py

echo Service script created: django_service.py

echo.
echo Step 3: Installing Windows service...
python django_service.py install
if errorlevel 1 (
    echo ERROR: Failed to install Windows service
    echo.
    echo TROUBLESHOOTING:
    echo 1. Make sure you're running as Administrator
    echo 2. Check if pywin32 is properly installed
    echo 3. Try: python -m win32serviceutil install
    echo.
    pause
    exit /b 1
)

echo.
echo Step 4: Starting Windows service...
python django_service.py start
if errorlevel 1 (
    echo ERROR: Failed to start Windows service
    echo.
    echo TROUBLESHOOTING:
    echo 1. Check Windows Event Viewer for errors
    echo 2. Try starting manually first
    echo 3. Check if port 8000 is available
    echo.
    echo Manual start command:
    echo python manage.py runserver 0.0.0.0:8000
    echo.
    pause
    exit /b 1
)

echo.
echo Step 5: Verifying service status...
timeout /t 10 /nobreak
sc query WaterBillingSystem
if errorlevel 1 (
    echo Warning: Could not query service status
) else (
    echo Service status checked successfully
)

echo.
echo Step 6: Testing web access...
timeout /t 5 /nobreak
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000' -UseBasicParsing -TimeoutSec 10; Write-Host 'Web server status:' $response.StatusCode } catch { Write-Host 'Web server not responding yet, may need more time to start' }"

echo.
echo ============================================
echo Windows Service Setup Complete!
echo ============================================
echo.
echo SERVICE INFORMATION:
echo - Service Name: WaterBillingSystem
echo - Display Name: Water Billing Management System
echo - Status: Should be running
echo - Startup Type: Manual (change to Automatic if desired)
echo.
echo SERVICE MANAGEMENT COMMANDS:
echo - Start:   net start WaterBillingSystem
echo - Stop:    net stop WaterBillingSystem
echo - Restart: net stop WaterBillingSystem ^&^& net start WaterBillingSystem
echo - Remove:  python django_service.py remove
echo.
echo ACCESS URLS:
echo - Local: http://localhost:8000
echo - Network: http://**************:8000
echo - Secondary: http://**************:8080
echo.
echo LOGIN:
echo - Username: admin
echo - Password: admin123
echo.
echo TO SET AUTOMATIC STARTUP:
echo 1. Open Services (services.msc)
echo 2. Find "Water Billing Management System"
echo 3. Right-click → Properties
echo 4. Startup type → Automatic
echo 5. Click OK
echo.
echo MONITORING:
echo - Check service: sc query WaterBillingSystem
echo - View logs: Event Viewer → Windows Logs → Application
echo - Test web: http://localhost:8000
echo.
echo Your Water Billing System is now running as a Windows Service!
echo It will continue running even when you log out.
echo.
pause
