# Water Billing Management System - Supabase Integration
# Optimized for Windows Server deployment

# Core Django framework
Django==5.2.4

# PostgreSQL database adapter - multiple fallback versions
# The deployment script will try these in order
psycopg2-binary==2.9.7

# Alternative PostgreSQL adapters (fallback options)
# asyncpg==0.28.0
# databases[postgresql]==0.8.0

# Configuration management
python-decouple==3.8

# Image processing for logos and media
Pillow==10.4.0

# HTTP requests library
requests==2.32.3

# Static file serving for production
whitenoise==6.5.0

# Windows service support
pywin32==306

# Additional production packages
gunicorn==21.2.0

# Security packages for production
django-cors-headers==4.3.1

# Backup and maintenance utilities
django-extensions==3.2.3
