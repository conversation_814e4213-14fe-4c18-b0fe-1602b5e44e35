{% extends 'base.html' %}
{% load currency_filters %}

{% block title %}Billing Details - {{ billing.client.get_full_name }}{% endblock %}

{% block page_title %}Billing Details{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'billing:billing_list' %}">Billing</a></li>
<li class="breadcrumb-item active">{{ billing.billing_month|date:"M Y" }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Billing Statement - {{ billing.billing_month|date:"F Y" }}</h3>
                <div class="card-tools">
                    <a href="{% url 'billing:billing_update' billing.pk %}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <button onclick="window.print()" class="btn btn-info">
                        <i class="fas fa-print"></i> Print
                    </button>
                    <a href="{% url 'billing:billing_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Client Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>Client Information</h5>
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">Client Code:</th>
                                <td><strong>{{ billing.client.code }}</strong></td>
                            </tr>
                            <tr>
                                <th>Name:</th>
                                <td>{{ billing.client.get_full_name }}</td>
                            </tr>
                            <tr>
                                <th>Address:</th>
                                <td>{{ billing.client.address|linebreaks }}</td>
                            </tr>
                            <tr>
                                <th>Contact:</th>
                                <td>{{ billing.client.contact|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <th>Email:</th>
                                <td>{{ billing.client.email|default:"Not provided" }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5>Billing Information</h5>
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">Billing Month:</th>
                                <td><strong>{{ billing.billing_month|date:"F Y" }}</strong></td>
                            </tr>
                            <tr>
                                <th>Category:</th>
                                <td>{{ billing.client.category.name }}</td>
                            </tr>
                            <tr>
                                <th>Meter Number:</th>
                                <td>{{ billing.client.meter_number }}</td>
                            </tr>
                            <tr>
                                <th>Rate per m³:</th>
                                <td>{{ billing.rate_per_cubic_meter|currency }}</td>
                            </tr>
                            <tr>
                                <th>Due Date:</th>
                                <td>
                                    <strong>{{ billing.due_date|date:"M d, Y" }}</strong>
                                    {% if billing.status != 'paid' and billing.due_date < today %}
                                        <span class="badge badge-danger ml-2">Overdue</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Meter Readings -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5>Meter Readings & Consumption</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="bg-light">
                                    <tr>
                                        <th>Previous Reading</th>
                                        <th>Current Reading</th>
                                        <th>Consumption</th>
                                        <th>Rate per m³</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>{{ billing.previous_reading }} m³</td>
                                        <td>{{ billing.current_reading }} m³</td>
                                        <td><strong>{{ billing.consumption }} m³</strong></td>
                                        <td>{{ billing.rate_per_cubic_meter|currency }}</td>
                                        <td><strong>{{ billing.amount|currency }}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Billing Summary -->
                <div class="row mb-4">
                    <div class="col-md-6 offset-md-6">
                        <div class="table-responsive">
                            <table class="table">
                                <tr>
                                    <th>Subtotal:</th>
                                    <td class="text-right">{{ billing.amount|currency }}</td>
                                </tr>
                                {% if billing.penalty > 0 %}
                                <tr>
                                    <th>Penalty:</th>
                                    <td class="text-right text-danger">{{ billing.penalty|currency }}</td>
                                </tr>
                                {% endif %}
                                <tr class="bg-light">
                                    <th>Total Amount:</th>
                                    <th class="text-right">{{ billing.total_amount|currency }}</th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Payment Information -->
                <div class="row">
                    <div class="col-md-6">
                        <h5>Payment Status</h5>
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">Status:</th>
                                <td>
                                    {% if billing.status == 'paid' %}
                                        <span class="badge badge-success badge-lg">Paid</span>
                                    {% elif billing.status == 'pending' %}
                                        <span class="badge badge-warning badge-lg">Pending</span>
                                    {% else %}
                                        <span class="badge badge-danger badge-lg">Overdue</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% if billing.paid_date %}
                            <tr>
                                <th>Paid Date:</th>
                                <td><strong>{{ billing.paid_date|date:"M d, Y" }}</strong></td>
                            </tr>
                            {% endif %}
                            {% if billing.notes %}
                            <tr>
                                <th>Notes:</th>
                                <td>{{ billing.notes|linebreaks }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5>System Information</h5>
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">Created By:</th>
                                <td>{{ billing.created_by.get_full_name|default:billing.created_by.username }}</td>
                            </tr>
                            <tr>
                                <th>Created Date:</th>
                                <td>{{ billing.created_at|date:"M d, Y H:i" }}</td>
                            </tr>
                            <tr>
                                <th>Last Updated:</th>
                                <td>{{ billing.updated_at|date:"M d, Y H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">Quick Actions</h3>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if billing.status != 'paid' %}
                    <a href="{% url 'billing:billing_update' billing.pk %}" class="btn btn-success btn-block">
                        <i class="fas fa-check"></i> Mark as Paid
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'clients:client_detail' billing.client.pk %}" class="btn btn-info btn-block">
                        <i class="fas fa-user"></i> View Client Details
                    </a>
                    
                    <a href="{% url 'clients:client_billing_history' billing.client.pk %}" class="btn btn-warning btn-block">
                        <i class="fas fa-history"></i> Client Billing History
                    </a>
                    
                    <button onclick="window.print()" class="btn btn-secondary btn-block">
                        <i class="fas fa-print"></i> Print Statement
                    </button>
                </div>
            </div>
        </div>
        
        {% if billing.status != 'paid' and billing.due_date < today %}
        <div class="card card-danger">
            <div class="card-header">
                <h3 class="card-title">Overdue Notice</h3>
            </div>
            <div class="card-body">
                <p><strong>This bill is overdue!</strong></p>
                <p>Due date was: {{ billing.due_date|date:"M d, Y" }}</p>
                <p>Days overdue: {{ today|timesince:billing.due_date }}</p>
                <p>Please contact the client for payment.</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<style>
@media print {
    .card-tools, .btn, .sidebar, .main-header, .main-footer, .content-header {
        display: none !important;
    }
    .content-wrapper {
        margin-left: 0 !important;
    }
}
</style>
{% endblock %}
