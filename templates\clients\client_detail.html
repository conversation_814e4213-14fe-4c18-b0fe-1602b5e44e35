{% extends 'base.html' %}
{% load currency_filters %}

{% block title %}{{ client.get_full_name }} - Client Details{% endblock %}

{% block page_title %}Client Details{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'clients:client_list' %}">Clients</a></li>
<li class="breadcrumb-item active">{{ client.code }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">{{ client.get_full_name }}</h3>
                <div class="card-tools">
                    <a href="{% url 'clients:client_update' client.pk %}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{% url 'clients:client_billing_history' client.pk %}" class="btn btn-info">
                        <i class="fas fa-history"></i> Billing History
                    </a>
                    <a href="{% url 'clients:client_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Personal Information</h5>
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">Client Code:</th>
                                <td><strong>{{ client.code }}</strong></td>
                            </tr>
                            <tr>
                                <th>Full Name:</th>
                                <td>{{ client.get_full_name }}</td>
                            </tr>
                            <tr>
                                <th>Contact:</th>
                                <td>{{ client.contact|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <th>Email:</th>
                                <td>{{ client.email|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <th>Address:</th>
                                <td>{{ client.address|linebreaks }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5>Billing Information</h5>
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">Category:</th>
                                <td>
                                    <span class="badge badge-info">{{ client.category.name }}</span>
                                </td>
                            </tr>
                            <tr>
                                <th>Rate per m³:</th>
                                <td><strong>{{ client.category.price_per_cubic_meter|currency }}</strong></td>
                            </tr>
                            <tr>
                                <th>Meter Number:</th>
                                <td><strong>{{ client.meter_number }}</strong></td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    {% if client.status %}
                                        <span class="badge badge-success">Active</span>
                                    {% else %}
                                        <span class="badge badge-danger">Inactive</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Created:</th>
                                <td>{{ client.created_at|date:"M d, Y" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">Billing Summary</h3>
            </div>
            <div class="card-body">
                <div class="info-box">
                    <span class="info-box-icon bg-info"><i class="fas fa-file-invoice"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Bills</span>
                        <span class="info-box-number">{{ billing_count }}</span>
                    </div>
                </div>
                
                {% if billings %}
                <h5>Recent Billings:</h5>
                <div class="timeline">
                    {% for billing in billings|slice:":3" %}
                    <div class="time-label">
                        <span class="bg-info">{{ billing.billing_month|date:"M Y" }}</span>
                    </div>
                    <div>
                        <i class="fas fa-file-invoice bg-blue"></i>
                        <div class="timeline-item">
                            <h3 class="timeline-header">
                                <strong>{{ billing.total_amount|currency }}</strong>
                                {% if billing.status == 'paid' %}
                                    <span class="badge badge-success">Paid</span>
                                {% elif billing.status == 'pending' %}
                                    <span class="badge badge-warning">Pending</span>
                                {% else %}
                                    <span class="badge badge-danger">Overdue</span>
                                {% endif %}
                            </h3>
                            <div class="timeline-body">
                                Consumption: {{ billing.consumption }} m³<br>
                                Due: {{ billing.due_date|date:"M d, Y" }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="mt-3">
                    <a href="{% url 'clients:client_billing_history' client.pk %}" class="btn btn-sm btn-info">
                        <i class="fas fa-history"></i> View All Billing History
                    </a>
                </div>
                {% else %}
                <p class="text-muted">No billing records found for this client.</p>
                <a href="{% url 'billing:billing_create' %}?client={{ client.pk }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> Create First Bill
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
