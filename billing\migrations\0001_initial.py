# Generated by Django 5.2.4 on 2025-07-03 16:28

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("clients", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Billing",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "billing_month",
                    models.DateField(help_text="Month and year for this billing"),
                ),
                (
                    "previous_reading",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Previous meter reading",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0"))
                        ],
                    ),
                ),
                (
                    "current_reading",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Current meter reading",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0"))
                        ],
                    ),
                ),
                (
                    "consumption",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Water consumption in cubic meters",
                        max_digits=10,
                    ),
                ),
                (
                    "rate_per_cubic_meter",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Rate per cubic meter at time of billing",
                        max_digits=10,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total amount to be paid",
                        max_digits=10,
                    ),
                ),
                (
                    "penalty",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Penalty amount for late payment",
                        max_digits=10,
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total amount including penalty",
                        max_digits=10,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("paid", "Paid"),
                            ("overdue", "Overdue"),
                        ],
                        default="pending",
                        max_length=10,
                    ),
                ),
                ("due_date", models.DateField()),
                ("paid_date", models.DateField(blank=True, null=True)),
                ("notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="billings",
                        to="clients.client",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_billings",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Billing",
                "verbose_name_plural": "Billings",
                "db_table": "billings",
                "ordering": ["-billing_month", "client__lastname"],
                "unique_together": {("client", "billing_month")},
            },
        ),
    ]
