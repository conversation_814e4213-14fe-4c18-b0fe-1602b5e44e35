from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q, Sum
from django.http import HttpResponse
from django.template.loader import render_to_string
from datetime import datetime
import csv
import json
from .models import Billing
from .forms import BillingForm
from clients.models import Client
from accounts.views import StaffOrAdminRequiredMixin, AdminRequiredMixin

class BillingListView(LoginRequiredMixin, StaffOrAdminRequiredMixin, ListView):
    model = Billing
    template_name = 'billing/billing_list.html'
    context_object_name = 'billings'
    paginate_by = 10

    def get_queryset(self):
        queryset = Billing.objects.select_related('client', 'client__category', 'created_by').order_by('-billing_month')
        search = self.request.GET.get('search')
        status = self.request.GET.get('status')

        if search:
            queryset = queryset.filter(
                Q(client__code__icontains=search) |
                Q(client__firstname__icontains=search) |
                Q(client__lastname__icontains=search)
            )

        if status:
            queryset = queryset.filter(status=status)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Billing.BILLING_STATUS
        return context

class BillingCreateView(LoginRequiredMixin, StaffOrAdminRequiredMixin, CreateView):
    model = Billing
    form_class = BillingForm
    template_name = 'billing/billing_form.html'
    success_url = reverse_lazy('billing:billing_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        form.instance.rate_per_cubic_meter = form.instance.client.category.price_per_cubic_meter
        messages.success(self.request, 'Billing created successfully.')
        return super().form_valid(form)

class BillingUpdateView(LoginRequiredMixin, UpdateView):
    model = Billing
    form_class = BillingForm
    template_name = 'billing/billing_form.html'
    success_url = reverse_lazy('billing:billing_list')

    def form_valid(self, form):
        messages.success(self.request, 'Billing updated successfully.')
        return super().form_valid(form)

class BillingDeleteView(LoginRequiredMixin, DeleteView):
    model = Billing
    template_name = 'billing/billing_confirm_delete.html'
    success_url = reverse_lazy('billing:billing_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Billing deleted successfully.')
        return super().delete(request, *args, **kwargs)

@login_required
def billing_detail(request, pk):
    billing = get_object_or_404(Billing, pk=pk)
    context = {
        'billing': billing,
    }
    return render(request, 'billing/billing_detail.html', context)

@login_required
def monthly_report(request):
    month = request.GET.get('month')
    year = request.GET.get('year')

    if not month or not year:
        current_date = datetime.now()
        month = current_date.month
        year = current_date.year
    else:
        month = int(month)
        year = int(year)

    billings = Billing.objects.filter(
        billing_month__month=month,
        billing_month__year=year
    ).select_related('client', 'client__category').order_by('client__lastname', 'client__firstname')

    # Calculate totals
    total_amount = billings.aggregate(total=Sum('total_amount'))['total'] or 0
    total_consumption = billings.aggregate(total=Sum('consumption'))['total'] or 0

    context = {
        'billings': billings,
        'month': month,
        'year': year,
        'month_name': datetime(year, month, 1).strftime('%B'),
        'total_amount': total_amount,
        'total_consumption': total_consumption,
        'billing_count': billings.count(),
    }

    return render(request, 'billing/monthly_report.html', context)

@login_required
def print_monthly_report(request):
    month = request.GET.get('month')
    year = request.GET.get('year')

    if not month or not year:
        current_date = datetime.now()
        month = current_date.month
        year = current_date.year
    else:
        month = int(month)
        year = int(year)

    billings = Billing.objects.filter(
        billing_month__month=month,
        billing_month__year=year
    ).select_related('client', 'client__category').order_by('client__lastname', 'client__firstname')

    # Calculate totals
    total_amount = billings.aggregate(total=Sum('total_amount'))['total'] or 0
    total_consumption = billings.aggregate(total=Sum('consumption'))['total'] or 0

    context = {
        'billings': billings,
        'month': month,
        'year': year,
        'month_name': datetime(year, month, 1).strftime('%B'),
        'total_amount': total_amount,
        'total_consumption': total_consumption,
        'billing_count': billings.count(),
    }

    return render(request, 'billing/print_monthly_report.html', context)

@login_required
def export_monthly_report_csv(request):
    """Export monthly report as CSV"""
    month = request.GET.get('month')
    year = request.GET.get('year')

    if not month or not year:
        current_date = datetime.now()
        month = current_date.month
        year = current_date.year
    else:
        month = int(month)
        year = int(year)

    billings = Billing.objects.filter(
        billing_month__month=month,
        billing_month__year=year
    ).select_related('client', 'client__category').order_by('client__lastname', 'client__firstname')

    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    month_name = datetime(year, month, 1).strftime('%B')
    filename = f'monthly_billing_report_{month_name}_{year}.csv'
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    writer = csv.writer(response)

    # Write header
    writer.writerow([
        'Client Code', 'Client Name', 'Category', 'Meter Number',
        'Previous Reading', 'Current Reading', 'Consumption (m³)',
        'Rate per m³', 'Amount', 'Penalty', 'Total Amount', 'Status',
        'Due Date', 'Paid Date'
    ])

    # Write data rows
    for billing in billings:
        writer.writerow([
            billing.client.code,
            billing.client.get_full_name(),
            billing.client.category.name,
            billing.client.meter_number,
            billing.previous_reading,
            billing.current_reading,
            billing.consumption,
            billing.rate_per_cubic_meter,
            billing.amount,
            billing.penalty,
            billing.total_amount,
            billing.get_status_display(),
            billing.due_date.strftime('%Y-%m-%d'),
            billing.paid_date.strftime('%Y-%m-%d') if billing.paid_date else ''
        ])

    # Write summary row
    total_amount = billings.aggregate(total=Sum('total_amount'))['total'] or 0
    total_consumption = billings.aggregate(total=Sum('consumption'))['total'] or 0

    writer.writerow([])  # Empty row
    writer.writerow(['SUMMARY'])
    writer.writerow(['Total Bills', billings.count()])
    writer.writerow(['Total Consumption', f'{total_consumption} m³'])
    writer.writerow(['Total Amount', f'₹{total_amount:,.2f}'])

    return response

@login_required
def export_monthly_report_json(request):
    """Export monthly report as JSON"""
    month = request.GET.get('month')
    year = request.GET.get('year')

    if not month or not year:
        current_date = datetime.now()
        month = current_date.month
        year = current_date.year
    else:
        month = int(month)
        year = int(year)

    billings = Billing.objects.filter(
        billing_month__month=month,
        billing_month__year=year
    ).select_related('client', 'client__category').order_by('client__lastname', 'client__firstname')

    # Prepare data
    billing_data = []
    for billing in billings:
        billing_data.append({
            'client_code': billing.client.code,
            'client_name': billing.client.get_full_name(),
            'category': billing.client.category.name,
            'meter_number': billing.client.meter_number,
            'previous_reading': float(billing.previous_reading),
            'current_reading': float(billing.current_reading),
            'consumption': float(billing.consumption),
            'rate_per_cubic_meter': float(billing.rate_per_cubic_meter),
            'amount': float(billing.amount),
            'penalty': float(billing.penalty),
            'total_amount': float(billing.total_amount),
            'status': billing.status,
            'due_date': billing.due_date.strftime('%Y-%m-%d'),
            'paid_date': billing.paid_date.strftime('%Y-%m-%d') if billing.paid_date else None
        })

    # Calculate totals
    total_amount = billings.aggregate(total=Sum('total_amount'))['total'] or 0
    total_consumption = billings.aggregate(total=Sum('consumption'))['total'] or 0

    data = {
        'report_info': {
            'month': month,
            'year': year,
            'month_name': datetime(year, month, 1).strftime('%B'),
            'generated_at': datetime.now().isoformat()
        },
        'summary': {
            'total_bills': billings.count(),
            'total_consumption': float(total_consumption),
            'total_amount': float(total_amount)
        },
        'billings': billing_data
    }

    response = HttpResponse(
        json.dumps(data, indent=2),
        content_type='application/json'
    )
    month_name = datetime(year, month, 1).strftime('%B')
    filename = f'monthly_billing_report_{month_name}_{year}.json'
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    return response
