{% extends 'base.html' %}

{% block title %}Profile - Water Billing Management System{% endblock %}

{% block page_title %}My Profile{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item active">Profile</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">Profile Information</h3>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <th width="30%">Username:</th>
                        <td>{{ user.username }}</td>
                    </tr>
                    <tr>
                        <th>Full Name:</th>
                        <td>{{ user.get_full_name|default:"Not provided" }}</td>
                    </tr>
                    <tr>
                        <th>Email:</th>
                        <td>{{ user.email|default:"Not provided" }}</td>
                    </tr>
                    <tr>
                        <th>Phone:</th>
                        <td>{{ user.phone|default:"Not provided" }}</td>
                    </tr>
                    <tr>
                        <th>Address:</th>
                        <td>{{ user.address|default:"Not provided"|linebreaks }}</td>
                    </tr>
                    <tr>
                        <th>User Type:</th>
                        <td>
                            {% if user.user_type == 'admin' %}
                                <span class="badge badge-danger">Administrator</span>
                            {% else %}
                                <span class="badge badge-info">Staff</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>Status:</th>
                        <td>
                            {% if user.is_active %}
                                <span class="badge badge-success">Active</span>
                            {% else %}
                                <span class="badge badge-secondary">Inactive</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>Last Login:</th>
                        <td>
                            {% if user.last_login %}
                                {{ user.last_login|date:"M d, Y H:i" }}
                            {% else %}
                                <span class="text-muted">Never</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>Date Joined:</th>
                        <td>{{ user.date_joined|date:"M d, Y H:i" }}</td>
                    </tr>
                </table>
            </div>
            <div class="card-footer">
                <a href="{% url 'accounts:update_profile' %}" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Update Profile
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">Account Statistics</h3>
            </div>
            <div class="card-body">
                <div class="info-box">
                    <span class="info-box-icon bg-info"><i class="fas fa-calendar"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Account Age</span>
                        <span class="info-box-number">{{ user.date_joined|timesince }}</span>
                    </div>
                </div>
                
                {% if user.user_type == 'admin' %}
                <div class="info-box">
                    <span class="info-box-icon bg-success"><i class="fas fa-user-shield"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Access Level</span>
                        <span class="info-box-number">Full Access</span>
                    </div>
                </div>
                {% endif %}
                
                <div class="info-box">
                    <span class="info-box-icon bg-warning"><i class="fas fa-clock"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Last Activity</span>
                        <span class="info-box-number">
                            {% if user.last_login %}
                                {{ user.last_login|timesince }} ago
                            {% else %}
                                Never
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card card-warning">
            <div class="card-header">
                <h3 class="card-title">Security</h3>
            </div>
            <div class="card-body">
                <p>Keep your account secure by:</p>
                <ul>
                    <li>Using a strong password</li>
                    <li>Logging out when finished</li>
                    <li>Not sharing your credentials</li>
                    <li>Updating your profile information regularly</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
