from django.contrib import admin
from .models import Billing

@admin.register(Billing)
class BillingAdmin(admin.ModelAdmin):
    list_display = ('client', 'billing_month', 'consumption', 'total_amount', 'status', 'due_date', 'created_at')
    list_filter = ('status', 'billing_month', 'created_at')
    search_fields = ('client__code', 'client__firstname', 'client__lastname')
    ordering = ('-billing_month', 'client__lastname')
    readonly_fields = ('consumption', 'amount', 'total_amount')

    fieldsets = (
        ('Client Information', {
            'fields': ('client', 'billing_month')
        }),
        ('Meter Readings', {
            'fields': ('previous_reading', 'current_reading', 'consumption')
        }),
        ('Billing Details', {
            'fields': ('rate_per_cubic_meter', 'amount', 'penalty', 'total_amount')
        }),
        ('Payment Information', {
            'fields': ('status', 'due_date', 'paid_date', 'notes')
        }),
        ('System Information', {
            'fields': ('created_by',),
            'classes': ('collapse',)
        })
    )
