{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}Water Billing Management System{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{% static 'img/icons/favicon.svg' %}">
    <link rel="alternate icon" href="{% static 'img/icons/favicon.ico' %}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    <!-- Responsive CSS -->
    <link rel="stylesheet" href="{% static 'css/responsive.css' %}">
    <!-- Print CSS -->
    <link rel="stylesheet" href="{% static 'css/print.css' %}" media="print">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    {% block extra_css %}{% endblock %}
    
    <style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --gold-accent: #FFD700;
        --sidebar-width: 250px;
    }
    
    body {
        font-family: 'Poppins', sans-serif;
        background-color: #f8f9fc;
    }
    </style>
</head>
<body class="hold-transition sidebar-mini layout-fixed {% block body_class %}{% endblock %}">
    <div class="wrapper">
        {% if user.is_authenticated %}
            <!-- Navbar -->
            <nav class="main-header navbar navbar-expand navbar-white navbar-light shadow-sm" style="background: white; border-bottom: 1px solid #eaeaea;">
                <!-- Left navbar links -->
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" data-widget="pushmenu" href="#" role="button">
                            <i class="fas fa-bars" style="color: #6c757d;"></i>
                        </a>
                    </li>
                    <li class="nav-item d-none d-sm-inline-block">
                        <a href="{% url 'dashboard:home' %}" class="nav-link" style="color: #495057; font-weight: 500;">
                            <i class="fas fa-home mr-1"></i> Home
                        </a>
                    </li>
                </ul>
                
                <!-- Right navbar links -->
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="d-flex align-items-center">
                                <div class="mr-2">
                                    <i class="far fa-user-circle fa-lg" style="color: #667eea;"></i>
                                </div>
                                <div>
                                    <span style="color: #495057; font-weight: 500;">
                                        {{ user.get_full_name|default:user.username }}
                                    </span>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow border-0" aria-labelledby="navbarDropdown" style="border-radius: 8px; margin-top: 8px;">
                            <li>
                                <a href="{% url 'accounts:profile' %}" class="dropdown-item d-flex align-items-center py-2">
                                    <i class="fas fa-user-circle me-3" style="width: 20px; color: #667eea;"></i>
                                    <span>Profile</span>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider my-1"></li>
                            <li>
                                <form action="{% url 'accounts:logout' %}" method="post" class="d-inline w-100">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item d-flex align-items-center py-2" 
                                            style="background: none; border: none; cursor: pointer; width: 100%;">
                                        <i class="fas fa-sign-out-alt me-3" style="width: 20px; color: #dc3545;"></i>
                                        <span style="color: #dc3545;">Logout</span>
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
            
            <!-- Main Sidebar Container -->
            <aside class="main-sidebar sidebar-dark-primary elevation-4" style="background: #2c3e50; box-shadow: 3px 0 10px rgba(0,0,0,0.1);">
                <!-- Brand Logo -->
                <a href="{% url 'dashboard:home' %}" class="brand-link d-flex align-items-center py-3" 
                   style="background: #1a252f; border-bottom: 1px solid rgba(255,255,255,0.1);">
                    <div class="brand-image d-flex align-items-center justify-content-center" 
                         style="width: 45px; height: 45px; border-radius: 10px; background: white; margin-left: 15px;">
                        <img src="{% static 'img/logo/logo.jpeg' %}" alt="Ankur Logo"
                             style="max-height: 40px; border-radius: 8px; border: 2px solid var(--gold-accent);">
                    </div>
                    <div class="brand-text ml-3">
                        <span style="font-size: 16px; font-weight: 600; color: rgb(255, 255, 255);">Water Billing</span><br>
                        <small style="font-size: 12px; color: rgba(255,255,255,0.7);">Management System</small>
                    </div>
                </a>
                
                <!-- Sidebar -->
                <div class="sidebar" style="padding: 10px 0;">
                    <!-- Sidebar Menu -->
                    <nav class="mt-2">
                        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                            <li class="nav-item">
                                <a href="{% url 'dashboard:home' %}" class="nav-link {% if request.resolver_match.app_name == 'dashboard' %}active{% endif %}">
                                    <i class="nav-icon fas fa-tachometer-alt"></i>
                                    <p>Dashboard</p>
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a href="{% url 'categories:category_list' %}" class="nav-link {% if request.resolver_match.app_name == 'categories' %}active{% endif %}">
                                    <i class="nav-icon fas fa-tags"></i>
                                    <p>Categories</p>
                                </a>
                            </li>
                            
                            {% if user.user_type == 'admin' or user.user_type == 'staff' %}
                            <li class="nav-item">
                                <a href="{% url 'clients:client_list' %}" class="nav-link {% if request.resolver_match.app_name == 'clients' %}active{% endif %}">
                                    <i class="nav-icon fas fa-users"></i>
                                    <p>Clients</p>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% if user.user_type == 'admin' or user.user_type == 'staff' %}
                            <li class="nav-item">
                                <a href="#" class="nav-link {% if request.resolver_match.app_name == 'billing' %}active{% endif %}">
                                    <i class="nav-icon fas fa-file-invoice-dollar"></i>
                                    <p>
                                        Billing
                                        <i class="fas fa-angle-left right"></i>
                                    </p>
                                </a>
                                <ul class="nav nav-treeview">
                                    <li class="nav-item">
                                        <a href="{% url 'billing:billing_list' %}" class="nav-link {% if request.resolver_match.url_name == 'billing_list' %}active{% endif %}">
                                            <i class="far fa-circle nav-icon"></i>
                                            <p>All Billings</p>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="{% url 'billing:billing_create' %}" class="nav-link {% if request.resolver_match.url_name == 'billing_create' %}active{% endif %}">
                                            <i class="far fa-circle nav-icon"></i>
                                            <p>Create Billing</p>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="{% url 'billing:monthly_report' %}" class="nav-link {% if request.resolver_match.url_name == 'monthly_report' %}active{% endif %}">
                                            <i class="far fa-circle nav-icon"></i>
                                            <p>Monthly Reports</p>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            {% endif %}
                            
                            <!-- Profile - Available to all users -->
                            <li class="nav-item">
                                <a href="{% url 'accounts:profile' %}" class="nav-link {% if request.resolver_match.url_name == 'profile' %}active{% endif %}">
                                    <i class="nav-icon fas fa-user-circle"></i>
                                    <p>My Profile</p>
                                </a>
                            </li>

                            <!-- Admin Only Features -->
                            {% if user.user_type == 'admin' %}
                            <li class="nav-item">
                                <a href="{% url 'accounts:user_list' %}" class="nav-link {% if request.resolver_match.url_name == 'user_list' %}active{% endif %}">
                                    <i class="nav-icon fas fa-user-cog"></i>
                                    <p>User Management</p>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{% url 'accounts:system_info' %}" class="nav-link {% if request.resolver_match.url_name == 'system_info' %}active{% endif %}">
                                    <i class="nav-icon fas fa-cog"></i>
                                    <p>System Settings</p>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </aside>
            
            <!-- Content Wrapper -->
            <div class="content-wrapper" style="background-color: #f8f9fc;">
                <!-- Content Header -->
                <div class="content-header pt-4 pb-3" style="background: white; border-bottom: 1px solid #eaeaea;">
                    <div class="container-fluid">
                        <div class="row align-items-center">
                            <div class="col-sm-6">
                                <h1 class="m-0" style="font-weight: 600; color: #2c3e50;">
                                    {% block page_title %}Dashboard{% endblock %}
                                </h1>
                            </div>
                            <div class="col-sm-6">
                                <ol class="breadcrumb float-sm-right mb-0">
                                    {% block breadcrumb %}
                                    <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}" style="color: #6c757d;">Home</a></li>
                                    <li class="breadcrumb-item active" style="color: #495057;">Dashboard</li>
                                    {% endblock %}
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Main content -->
                <section class="content py-3">
                    <div class="container-fluid">
                        <!-- Enhanced Messages -->
                        {% if messages %}
                            <div class="mb-4">
                                {% for message in messages %}
                                    <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show shadow-sm" role="alert">
                                        <div class="d-flex align-items-center">
                                            {% if message.tags == 'success' %}
                                                <i class="fas fa-check-circle me-2"></i>
                                            {% elif message.tags == 'error' or message.tags == 'danger' %}
                                                <i class="fas fa-exclamation-circle me-2"></i>
                                            {% elif message.tags == 'warning' %}
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                            {% else %}
                                                <i class="fas fa-info-circle me-2"></i>
                                            {% endif %}
                                            <span>{{ message }}</span>
                                        </div>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        {% block content %}{% endblock %}
                    </div>
                </section>
            </div>
            
            <!-- Footer -->
            <footer class="main-footer py-3" style="background: #2c3e50; color: rgba(255,255,255,0.7); border-top: 1px solid rgba(255,255,255,0.1);">
                <div class="container-fluid">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <strong>Copyright &copy; {% now "Y" %} <a href="#" style="color: var(--gold-accent);">Ankur Technologies</a>.</strong>
                            All rights reserved.
                        </div>
                        <div class="col-md-6 text-md-end">
                            <span style="font-size: 14px;">
                                <b style="color: var(--gold-accent);">Version</b> 
                                <span>1.0.0</span>
                            </span>
                        </div>
                    </div>
                </div>
            </footer>
        {% else %}
            <!-- Minimal Navbar for Unauthenticated Users -->
            <nav class="main-header navbar navbar-expand navbar-white navbar-light shadow-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 8px 15px;">
                <div class="container-fluid px-0">
                    <a class="navbar-brand d-flex align-items-center py-0" href="#" style="margin: 0;">
                        <img src="{% static 'img/logo/logo.jpeg' %}" alt="Ankur Logo"
                             style="height: 40px; border-radius: 8px; border: 2px solid #FFD700; margin-right: 12px;">
                        <span style="color: white; font-weight: bold; font-size: 18px; letter-spacing: 0.5px;">Water Billing</span>
                    </a>
                    
                    <ul class="navbar-nav ml-auto">
                        <li class="nav-item mx-1">
                            <a href="{% url 'accounts:login' %}" class="nav-link btn btn-sm px-3 py-2" 
                               style="color: white; background: rgba(255,255,255,0.15); border-radius: 6px;">
                                <i class="fas fa-sign-in-alt me-1"></i> Login
                            </a>
                        </li>
                        <li class="nav-item mx-1">
                            <a href="{% url 'accounts:register' %}" class="nav-link btn btn-sm px-3 py-2" 
                               style="color: white; background: rgba(255,215,0,0.2); border-radius: 6px;">
                                <i class="fas fa-user-plus me-1"></i> Register
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Login/Register Content -->
            {% block login_content %}{% endblock %}
        {% endif %}
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE JS -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/custom.js' %}"></script>

    <style>
    /* Global Styles */
    body {
        font-family: 'Poppins', sans-serif;
        color: #495057;
    }
    
    /* Sidebar Styling */
    .main-sidebar {
        background-color: #2c3e50;
    }
    
    .nav-sidebar > .nav-item > .nav-link {
        color: rgba(255,255,255,0.8);
        padding: 0.8rem 1.2rem;
        margin: 2px 8px;
        border-radius: 6px;
        transition: all 0.3s;
    }
    
    .nav-sidebar > .nav-item > .nav-link:hover {
        background: rgba(255,255,255,0.1);
        color: white;
    }
    
    .nav-sidebar > .nav-item > .nav-link.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .nav-treeview > .nav-item > .nav-link {
        padding-left: 3rem;
    }
    
    /* Content Styling */
    .content-wrapper {
        background: #f8f9fc;
    }
    
    .content-header h1 {
        font-weight: 600;
        font-size: 1.8rem;
    }
    
    /* Card Styling */
    .card {
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        border: none;
        margin-bottom: 1.5rem;
    }
    
    .card-header {
        border-bottom: 1px solid rgba(0,0,0,0.08);
        background: white;
        padding: 1.2rem 1.5rem;
        border-radius: 10px 10px 0 0 !important;
    }
    
    .card-title {
        font-weight: 600;
        font-size: 1.2rem;
        margin-bottom: 0;
        color: #2c3e50;
    }
    
    /* Alert Styling */
    .alert {
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        border: none;
        padding: 1rem 1.25rem;
        font-size: 15px;
    }
    
    /* Table Styling */
    .table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 8px rgba(0,0,0,0.03);
    }
    
    .table th {
        background: #f8f9fc;
        font-weight: 600;
        color: #495057;
        padding: 1rem;
    }
    
    .table td {
        padding: 0.9rem 1rem;
        vertical-align: middle;
    }
    
    /* Form Styling */
    .form-control, .form-select {
        border-radius: 8px;
        padding: 0.75rem 1rem;
        border: 1px solid #e2e5ec;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
    }
    
    /* Button Styling */
    .btn {
        border-radius: 8px;
        font-weight: 500;
        padding: 0.6rem 1.25rem;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #5e72e4 0%, #6a45a5 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.1);
    }
    
    /* Animation */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .fade-in {
        animation: fadeIn 0.4s ease-out;
    }
    </style>

    <script>
    $(document).ready(function() {
        // Enhanced message handling
        $('.alert').each(function() {
            $(this).addClass('fade-in');
            
            // Auto-hide alerts after 7 seconds
            setTimeout(() => {
                $(this).fadeOut('slow');
            }, 7000);
        });

        // Handle close button clicks
        $(document).on('click', '.alert .btn-close', function() {
            $(this).closest('.alert').fadeOut('fast');
        });
    });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>