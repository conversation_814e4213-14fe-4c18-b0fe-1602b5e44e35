{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}Water Billing Management System{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{% static 'img/icons/favicon.svg' %}">
    <link rel="alternate icon" href="{% static 'img/icons/favicon.ico' %}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    <!-- Responsive CSS -->
    <link rel="stylesheet" href="{% static 'css/responsive.css' %}">
    <!-- Print CSS -->
    <link rel="stylesheet" href="{% static 'css/print.css' %}" media="print">

    {% block extra_css %}{% endblock %}
</head>
<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">
        {% if user.is_authenticated %}
            <!-- Navbar -->
            <nav class="main-header navbar navbar-expand navbar-white navbar-light">
                <!-- Left navbar links -->
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                    </li>
                </ul>
                
                <!-- Right navbar links -->
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="far fa-user"></i>
                            {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                            <li><a href="{% url 'accounts:profile' %}" class="dropdown-item">
                                <i class="fas fa-user me-2"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a href="{% url 'accounts:logout' %}" class="dropdown-item">
                                <i class="fas fa-sign-out-alt me-2"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </nav>
            
            <!-- Main Sidebar Container -->
            <aside class="main-sidebar sidebar-dark-primary elevation-4">
                <!-- Brand Logo -->
                <a href="{% url 'dashboard:home' %}" class="brand-link"
                   style="padding: 0.75rem 1rem;
                          display: flex;
                          align-items: center;
                          white-space: nowrap;
                          overflow: visible;
                          min-height: 60px;">
                    <!-- Logo -->
                    <img src="{% static 'img/logo/logo.jpeg' %}" alt="Ankur Logo"
                         class="brand-image img-fluid elevation-3"
                         style="opacity: 1;
                                max-height: 45px;
                                width: auto;
                                border-radius: 10px;
                                border: 3px solid #FFD700;
                                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                                transition: all 0.3s ease;
                                flex-shrink: 0;
                                margin-right: 10px;">
                    <span class="brand-text font-weight-bold"
                          style="color: #ffffff;
                                 font-size: 15px;
                                 text-shadow: 1px 1px 2px rgba(0,0,0,0.4);
                                 line-height: 1.3;
                                 overflow: visible;
                                 white-space: normal;
                                 display: flex;
                                 flex-direction: column;
                                 justify-content: center;">
                        <div style="font-size: 15px; font-weight: bold; margin-bottom: 2px;">
                            Water Billing
                        </div>
                        <div style="font-size: 12px; opacity: 0.9; font-weight: normal;">
                            Management System
                        </div>
                    </span>
                </a>
                
                <!-- Sidebar -->
                <div class="sidebar">
                    <!-- Sidebar Menu -->
                    <nav class="mt-2">
                        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                            <li class="nav-item">
                                <a href="{% url 'dashboard:home' %}" class="nav-link {% if request.resolver_match.app_name == 'dashboard' %}active{% endif %}">
                                    <i class="nav-icon fas fa-tachometer-alt"></i>
                                    <p>Dashboard</p>
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a href="{% url 'categories:category_list' %}" class="nav-link {% if request.resolver_match.app_name == 'categories' %}active{% endif %}">
                                    <i class="nav-icon fas fa-tags"></i>
                                    <p>Categories</p>
                                </a>
                            </li>
                            
                            {% if user.user_type == 'admin' or user.user_type == 'staff' %}
                            <li class="nav-item">
                                <a href="{% url 'clients:client_list' %}" class="nav-link {% if request.resolver_match.app_name == 'clients' %}active{% endif %}">
                                    <i class="nav-icon fas fa-users"></i>
                                    <p>Clients</p>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% if user.user_type == 'admin' or user.user_type == 'staff' %}
                            <li class="nav-item {% if request.resolver_match.app_name == 'billing' %}menu-open{% endif %}">
                                <a href="#" class="nav-link {% if request.resolver_match.app_name == 'billing' %}active{% endif %}">
                                    <i class="nav-icon fas fa-file-invoice-dollar"></i>
                                    <p>
                                        Billing
                                        <i class="fas fa-angle-left right"></i>
                                    </p>
                                </a>
                                <ul class="nav nav-treeview">
                                    <li class="nav-item">
                                        <a href="{% url 'billing:billing_list' %}" class="nav-link {% if request.resolver_match.url_name == 'billing_list' %}active{% endif %}">
                                            <i class="far fa-circle nav-icon"></i>
                                            <p>All Billings</p>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="{% url 'billing:billing_create' %}" class="nav-link {% if request.resolver_match.url_name == 'billing_create' %}active{% endif %}">
                                            <i class="far fa-circle nav-icon"></i>
                                            <p>Create Billing</p>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="{% url 'billing:monthly_report' %}" class="nav-link {% if request.resolver_match.url_name == 'monthly_report' %}active{% endif %}">
                                            <i class="far fa-circle nav-icon"></i>
                                            <p>Monthly Reports</p>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            {% endif %}
                            
                            <!-- Profile - Available to all users -->
                            <li class="nav-item">
                                <a href="{% url 'accounts:profile' %}" class="nav-link {% if request.resolver_match.url_name == 'profile' %}active{% endif %}">
                                    <i class="nav-icon fas fa-user-circle"></i>
                                    <p>My Profile</p>
                                </a>
                            </li>

                            <!-- Admin Only Features -->
                            {% if user.user_type == 'admin' %}
                            <li class="nav-item">
                                <a href="{% url 'accounts:user_list' %}" class="nav-link {% if request.resolver_match.url_name == 'user_list' %}active{% endif %}">
                                    <i class="nav-icon fas fa-user-cog"></i>
                                    <p>User Management</p>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{% url 'accounts:system_info' %}" class="nav-link {% if request.resolver_match.url_name == 'system_info' %}active{% endif %}">
                                    <i class="nav-icon fas fa-cog"></i>
                                    <p>System Settings</p>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </aside>
            
            <!-- Content Wrapper -->
            <div class="content-wrapper">
                <!-- Content Header -->
                <div class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1 class="m-0">{% block page_title %}Dashboard{% endblock %}</h1>
                            </div>
                            <div class="col-sm-6">
                                <ol class="breadcrumb float-sm-right">
                                    {% block breadcrumb %}
                                    <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
                                    <li class="breadcrumb-item active">Dashboard</li>
                                    {% endblock %}
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Main content -->
                <section class="content">
                    <div class="container-fluid">
                        <!-- Enhanced Messages -->
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                </div>
                            {% endfor %}
                        {% endif %}
                        
                        {% block content %}{% endblock %}
                    </div>
                </section>
            </div>
            
            <!-- Footer -->
            <footer class="main-footer" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-top: 3px solid #4CAF50;">
                <strong>Copyright &copy; 2025 <a href="#" style="color: #FFD700; text-decoration: none; font-weight: bold;">Ankur Technologies</a>.</strong>
                All rights reserved. | Water Billing Management System
                <div class="float-right d-none d-sm-inline-block">
                    <b style="color: #FFD700;">Version</b> <span style="color: #E8F5E8;">1.0.0</span>
                </div>
            </footer>
        {% else %}
            <!-- Minimal Navbar for Unauthenticated Users -->
            <nav class="main-header navbar navbar-expand navbar-white navbar-light" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="container">
                    <a class="navbar-brand" href="#">
                        <img src="{% static 'img/logo/logo.jpeg' %}" alt="Ankur Logo"
                             style="height: 40px; border-radius: 8px; border: 2px solid #FFD700; margin-right: 10px;">
                        <span style="color: white; font-weight: bold; font-size: 18px;">Water Billing</span>
                    </a>
                    
                    <ul class="navbar-nav ml-auto">
                        <li class="nav-item">
                            <a href="{% url 'accounts:login' %}" class="nav-link" style="color: white; font-weight: 500;">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{% url 'accounts:register' %}" class="nav-link" style="color: #FFD700; font-weight: 500;">
                                <i class="fas fa-user-plus"></i> Register
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Login/Register Content -->
            {% block login_content %}{% endblock %}
        {% endif %}
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE JS -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/custom.js' %}"></script>

    <!-- Enhanced Message Styling -->
    <style>
    /* Enhanced Alert Styling */
    .alert {
        position: relative;
        border-radius: 8px !important;
        font-weight: 600 !important;
        font-size: 15px !important;
        padding: 15px 20px !important;
        box-shadow: 0 3px 6px rgba(0,0,0,0.15) !important;
        animation: slideInDown 0.4s ease-out;
        margin-bottom: 20px !important;
    }

    .alert-success {
        background-color: #d4edda !important;
        border: 2px solid #28a745 !important;
        color: #155724 !important;
    }

    .alert-error, .alert-danger {
        background-color: #f8d7da !important;
        border: 2px solid #dc3545 !important;
        color: #721c24 !important;
    }

    .alert-warning {
        background-color: #fff3cd !important;
        border: 2px solid #ffc107 !important;
        color: #856404 !important;
    }

    .alert-info {
        background-color: #d1ecf1 !important;
        border: 2px solid #17a2b8 !important;
        color: #0c5460 !important;
    }

    .alert .close {
        position: absolute;
        top: 12px;
        right: 15px;
        background: none;
        border: none;
        font-size: 20px;
        opacity: 0.8;
        cursor: pointer;
        color: inherit;
    }

    .alert .close:hover {
        opacity: 1;
    }

    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Enhanced Logo Styling */
    .brand-link:hover .brand-image {
        transform: scale(1.05);
        box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;
    }

    .brand-link:hover .brand-text {
        color: #FFD700 !important;
    }

    /* Footer Enhancement */
    .main-footer a:hover {
        color: #FFFFFF !important;
        text-shadow: 0 0 5px #FFD700;
    }
    
    /* Unauthenticated Navbar Styling */
    .navbar-light .navbar-nav .nav-link {
        padding: 0.5rem 1rem;
        border-radius: 4px;
        transition: all 0.3s ease;
    }
    
    .navbar-light .navbar-nav .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
    </style>

    <script>
    $(document).ready(function() {
        // Enhanced message handling
        $('.alert').each(function() {
            var $alert = $(this);
            var messageType = '';

            // Determine message type and add appropriate icon
            if ($alert.hasClass('alert-success')) {
                messageType = 'success';
                $alert.prepend('<i class="fas fa-check-circle me-2"></i>');
            } else if ($alert.hasClass('alert-error') || $alert.hasClass('alert-danger')) {
                messageType = 'error';
                $alert.prepend('<i class="fas fa-exclamation-circle me-2"></i>');
            } else if ($alert.hasClass('alert-warning')) {
                messageType = 'warning';
                $alert.prepend('<i class="fas fa-exclamation-triangle me-2"></i>');
            } else {
                messageType = 'info';
                $alert.prepend('<i class="fas fa-info-circle me-2"></i>');
            }

            // Add close button if not present
            if (!$alert.find('.close').length) {
                $alert.append('<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>');
            }
        });

        // Auto-hide alerts after 7 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 7000);

        // Handle close button clicks
        $(document).on('click', '.alert .close', function() {
            $(this).parent('.alert').fadeOut('fast');
        });
    });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>