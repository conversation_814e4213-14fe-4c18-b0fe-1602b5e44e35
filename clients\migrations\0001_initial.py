# Generated by Django 5.2.4 on 2025-07-03 16:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("categories", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Client",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "code",
                    models.<PERSON>r<PERSON>ield(
                        help_text="Unique client code", max_length=50, unique=True
                    ),
                ),
                ("firstname", models.Char<PERSON>ield(max_length=100)),
                ("lastname", models.<PERSON>r<PERSON>ield(max_length=100)),
                ("middlename", models.<PERSON>r<PERSON>ield(blank=True, max_length=100, null=True)),
                ("contact", models.CharField(blank=True, max_length=20, null=True)),
                ("email", models.EmailField(blank=True, max_length=254, null=True)),
                ("address", models.TextField()),
                ("meter_number", models.Char<PERSON><PERSON>(max_length=100, unique=True)),
                (
                    "status",
                    models.<PERSON><PERSON>an<PERSON>ield(default=True, help_text="Active/Inactive"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="clients",
                        to="categories.category",
                    ),
                ),
            ],
            options={
                "verbose_name": "Client",
                "verbose_name_plural": "Clients",
                "db_table": "clients",
                "ordering": ["lastname", "firstname"],
            },
        ),
    ]
