@echo off
echo ============================================
echo Water Billing System - Supabase Deployment
echo ============================================
echo.
echo This script will deploy your app with Supabase integration
echo All users and clients will be stored in your Supabase database
echo.

REM Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed!
    echo.
    echo SOLUTION:
    echo 1. Download Python 3.11+ from: https://www.python.org/downloads/
    echo 2. During installation, CHECK "Add Python to PATH"
    echo 3. Restart this script after installation
    echo.
    pause
    exit /b 1
)

echo Python version: 
python --version

echo.
echo Step 1: Setting up virtual environment...
if exist venv (
    echo Removing old virtual environment...
    rmdir /s /q venv
)

python -m venv venv
if errorlevel 1 (
    echo ERROR: Failed to create virtual environment
    pause
    exit /b 1
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo.
echo Step 2: Upgrading pip and installing core packages...
python -m pip install --upgrade pip
pip install wheel setuptools

echo.
echo Step 3: Installing Django and basic dependencies...
pip install Django==5.2.4
pip install python-decouple==3.8
pip install Pillow==10.4.0
pip install requests==2.32.3
pip install whitenoise==6.5.0

echo.
echo Step 4: Installing PostgreSQL driver for Supabase...
echo Trying multiple methods to ensure compatibility...

REM Method 1: Try latest compatible version
echo Attempting psycopg2-binary 2.9.7...
pip install psycopg2-binary==2.9.7 --no-cache-dir
if not errorlevel 1 goto :postgres_success

REM Method 2: Try stable version
echo Attempting psycopg2-binary 2.9.5...
pip install psycopg2-binary==2.9.5 --no-cache-dir
if not errorlevel 1 goto :postgres_success

REM Method 3: Try with specific build options
echo Attempting with build isolation disabled...
pip install psycopg2-binary --no-build-isolation --no-cache-dir
if not errorlevel 1 goto :postgres_success

REM Method 4: Try pre-compiled wheel
echo Attempting pre-compiled wheel...
pip install --find-links https://download.lfd.uci.edu/pythonlibs/archived/ psycopg2-binary
if not errorlevel 1 goto :postgres_success

REM Method 5: Install Visual C++ and retry
echo.
echo PostgreSQL driver installation failed!
echo.
echo AUTOMATIC FIX: Installing Microsoft Visual C++ Build Tools...
echo This will enable PostgreSQL driver compilation...
echo.

REM Download and install Visual C++ redistributable
powershell -Command "& {Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vc_redist.x64.exe' -OutFile 'vc_redist.x64.exe'}"
if exist vc_redist.x64.exe (
    echo Installing Visual C++ Redistributable...
    vc_redist.x64.exe /quiet /norestart
    timeout /t 30 /nobreak
    del vc_redist.x64.exe
    
    echo Retrying PostgreSQL driver installation...
    pip install psycopg2-binary==2.9.7 --no-cache-dir
    if not errorlevel 1 goto :postgres_success
)

REM Method 6: Alternative PostgreSQL adapter
echo Trying alternative PostgreSQL adapter...
pip install asyncpg
pip install databases[postgresql]
if not errorlevel 1 goto :postgres_alt_success

echo.
echo ============================================
echo CRITICAL ERROR: PostgreSQL Driver Failed
echo ============================================
echo.
echo All automatic fixes failed. Manual intervention required.
echo.
echo MANUAL SOLUTIONS:
echo.
echo Option 1 - Install Visual Studio Build Tools:
echo 1. Download: https://visualstudio.microsoft.com/visual-cpp-build-tools/
echo 2. Install "C++ build tools" workload
echo 3. Restart and re-run this script
echo.
echo Option 2 - Use pre-compiled wheel:
echo 1. Download wheel from: https://www.lfd.uci.edu/~gohlke/pythonlibs/#psycopg
echo 2. Install: pip install downloaded_file.whl
echo 3. Continue with: python manage.py migrate
echo.
echo Option 3 - Contact system administrator for:
echo - Installing PostgreSQL client libraries
echo - Setting up development environment
echo.
pause
exit /b 1

:postgres_alt_success
echo Alternative PostgreSQL adapter installed successfully!
goto :continue_setup

:postgres_success
echo PostgreSQL driver installed successfully!

:continue_setup
echo.
echo Step 5: Installing Windows service support...
pip install pywin32==306
if errorlevel 1 (
    echo Warning: Windows service support failed (optional)
    echo App will still work without service functionality
)

echo.
echo Step 6: Verifying Supabase connection...
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'water_billing_system.settings')
import django
django.setup()
from django.db import connection
try:
    with connection.cursor() as cursor:
        cursor.execute('SELECT 1')
        print('✅ Supabase connection successful!')
except Exception as e:
    print('❌ Supabase connection failed:', str(e))
    print('Check your .env file configuration')
    exit(1)
"
if errorlevel 1 (
    echo.
    echo ERROR: Cannot connect to Supabase!
    echo.
    echo TROUBLESHOOTING:
    echo 1. Check internet connection
    echo 2. Verify .env file settings:
    type .env | findstr "DB_HOST\|DB_NAME\|DB_USER"
    echo 3. Test Supabase access from browser
    echo 4. Check firewall settings
    echo.
    pause
    exit /b 1
)

echo.
echo Step 7: Setting up database tables...
python manage.py migrate
if errorlevel 1 (
    echo ERROR: Database migration failed!
    echo Check Supabase connection and permissions
    pause
    exit /b 1
)

echo.
echo Step 8: Collecting static files...
python manage.py collectstatic --noinput
if errorlevel 1 (
    echo Warning: Static files collection failed
    echo App may not display properly
)

echo.
echo Step 9: Creating admin user and sample data...
python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'water_billing_system.settings')
django.setup()

from accounts.models import User
from categories.models import Category
from clients.models import Client

print('Creating admin user...')
if not User.objects.filter(username='admin').exists():
    admin = User.objects.create_superuser(
        username='admin',
        email='<EMAIL>', 
        password='admin123',
        user_type='admin'
    )
    print('✅ Admin user created in Supabase')
else:
    print('✅ Admin user already exists in Supabase')

print('Creating categories...')
categories = [
    {'name': 'Residential', 'price': 15.00, 'desc': 'For residential properties'},
    {'name': 'Commercial', 'price': 25.00, 'desc': 'For commercial establishments'}, 
    {'name': 'Industrial', 'price': 35.00, 'desc': 'For industrial facilities'}
]

for cat in categories:
    if not Category.objects.filter(name=cat['name']).exists():
        Category.objects.create(
            name=cat['name'],
            description=cat['desc'],
            price_per_cubic_meter=cat['price']
        )
        print(f'✅ Created category: {cat[\"name\"]} in Supabase')

print('✅ All data created in Supabase database!')
"

echo.
echo Step 10: Final verification...
python manage.py check --deploy
if errorlevel 1 (
    echo Warning: Deployment check found issues
    echo App should still work for development
)

echo.
echo ============================================
echo 🎉 SUPABASE DEPLOYMENT SUCCESSFUL! 🎉
echo ============================================
echo.
echo Your Water Billing Management System is ready!
echo All data will be stored in your Supabase database.
echo.
echo 🌐 ACCESS URLS:
echo - Local: http://localhost:8000
echo - Network: http://**************:8000  
echo - Secondary: http://**************:8080
echo.
echo 🔐 LOGIN CREDENTIALS:
echo - Username: admin
echo - Password: admin123
echo.
echo 📊 SUPABASE INTEGRATION:
echo ✅ Users → Stored in Supabase wbms_users table
echo ✅ Clients → Stored in Supabase wbms_clients table
echo ✅ Categories → Stored in Supabase wbms_categories table
echo ✅ Billings → Stored in Supabase wbms_billings table
echo.
echo 🚀 TO START SERVER:
echo 1. cd %CD%
echo 2. venv\Scripts\activate  
echo 3. python manage.py runserver 0.0.0.0:8000
echo.
echo Start server now? (Y/N)
set /p start="Enter choice: "
if /i "%start%"=="Y" (
    echo.
    echo 🌟 Starting Water Billing Management System...
    echo 📡 Connected to Supabase database
    echo 🌐 Server will be accessible at configured IPs
    echo.
    echo Press Ctrl+C to stop the server
    echo.
    python manage.py runserver 0.0.0.0:8000
) else (
    echo.
    echo To start the server later:
    echo cd %CD%
    echo venv\Scripts\activate
    echo python manage.py runserver 0.0.0.0:8000
    echo.
)

echo.
echo 📋 NEXT STEPS:
echo 1. Configure Windows Firewall (ports 8000, 8080)
echo 2. Set up router port forwarding  
echo 3. Test external access
echo 4. Add your clients and users
echo.
pause
