import win32serviceutil
import win32service
import win32event
import socket
import subprocess
import os
import sys

class DjangoService(win32serviceutil.ServiceFramework):
    _svc_name_ = "DjangoWaterBilling"
    _svc_display_name_ = "Django Water Billing Management System"
    _svc_description_ = "Water Billing Management System Web Application"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)

    def SvcDoRun(self):
        # Change to the directory where manage.py is located
        os.chdir(r'C:\WebApps\WaterBilling')
        
        # Activate virtual environment and run Django
        cmd = [
            r'C:\WebApps\WaterBilling\venv\Scripts\python.exe',
            'manage.py',
            'runserver',
            '0.0.0.0:8000'
        ]
        
        self.process = subprocess.Popen(cmd)
        
        # Wait for stop signal
        win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)
        
        # Terminate Django process
        if self.process:
            self.process.terminate()

if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(DjangoService)
