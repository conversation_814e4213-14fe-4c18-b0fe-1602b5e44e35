from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
from clients.models import Client
from accounts.models import User

class Billing(models.Model):
    BILLING_STATUS = (
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
    )

    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='billings')
    billing_month = models.DateField(help_text="Month and year for this billing")
    previous_reading = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Previous meter reading"
    )
    current_reading = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Current meter reading"
    )
    consumption = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Water consumption in cubic meters"
    )
    rate_per_cubic_meter = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Rate per cubic meter at time of billing"
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Total amount to be paid"
    )
    penalty = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Penalty amount for late payment"
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Total amount including penalty"
    )
    status = models.CharField(max_length=10, choices=BILLING_STATUS, default='pending')
    due_date = models.DateField()
    paid_date = models.DateField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_billings')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Calculate consumption
        self.consumption = self.current_reading - self.previous_reading
        # Calculate amount
        self.amount = self.consumption * self.rate_per_cubic_meter
        # Calculate total amount
        self.total_amount = self.amount + self.penalty
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.client.code} - {self.billing_month.strftime('%B %Y')}"

    class Meta:
        db_table = 'wbms_billings'
        verbose_name = 'Billing'
        verbose_name_plural = 'Billings'
        ordering = ['-billing_month', 'client__lastname']
        unique_together = ['client', 'billing_month']
