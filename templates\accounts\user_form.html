{% extends 'base.html' %}

{% block title %}{% if object %}Edit User{% else %}Add User{% endif %} - Water Billing Management System{% endblock %}

{% block page_title %}{% if object %}Edit User{% else %}Add New User{% endif %}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'accounts:user_list' %}">Users</a></li>
<li class="breadcrumb-item active">{% if object %}Edit{% else %}Add{% endif %}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">User Information</h3>
            </div>
            <form method="post">
                {% csrf_token %}
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.username.id_for_label }}">Username *</label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger">
                                        {% for error in form.username.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.email.id_for_label }}">Email Address</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger">
                                        {% for error in form.email.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.first_name.id_for_label }}">First Name</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.first_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.last_name.id_for_label }}">Last Name</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.last_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    {% if not object %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.password1.id_for_label }}">Password *</label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                    <div class="text-danger">
                                        {% for error in form.password1.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.password2.id_for_label }}">Confirm Password *</label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                    <div class="text-danger">
                                        {% for error in form.password2.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.user_type.id_for_label }}" class="form-label font-weight-bold text-dark" style="font-size: 16px; color: #333 !important;">
                                    <i class="fas fa-user-tag text-primary"></i> User Type *
                                </label>
                                {{ form.user_type }}
                                {% if form.user_type.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.user_type.errors %}
                                            <small><i class="fas fa-exclamation-circle"></i> {{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    <strong>Role Descriptions:</strong><br>
                                    <span class="badge badge-danger">Admin</span> - Full system access, user management<br>
                                    <span class="badge badge-info">Staff</span> - Billing operations, profile access<br>
                                    <span class="badge badge-secondary">User</span> - Profile access only
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.phone.id_for_label }}">Phone Number</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger">
                                        {% for error in form.phone.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.address.id_for_label }}">Address</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-danger">
                                {% for error in form.address.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                Active User
                            </label>
                        </div>
                        {% if form.is_active.errors %}
                            <div class="text-danger">
                                {% for error in form.is_active.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> {% if object %}Update{% else %}Create{% endif %} User
                    </button>
                    <a href="{% url 'accounts:user_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">User Roles</h3>
            </div>
            <div class="card-body">
                <h5><span class="badge badge-danger">Admin</span></h5>
                <p>Full system access including:</p>
                <ul>
                    <li>User management</li>
                    <li>System settings</li>
                    <li>All CRUD operations</li>
                    <li>Reports and analytics</li>
                </ul>
                
                <h5><span class="badge badge-info">Staff</span></h5>
                <p>Limited access for daily operations:</p>
                <ul>
                    <li>Client management</li>
                    <li>Billing operations</li>
                    <li>Category management</li>
                    <li>View reports</li>
                </ul>
            </div>
        </div>
        
        {% if object %}
        <div class="card card-warning">
            <div class="card-header">
                <h3 class="card-title">Password Reset</h3>
            </div>
            <div class="card-body">
                <p>To change the password for this user, you need to use the Django admin interface or create a separate password reset form.</p>
                <p><strong>Current user:</strong> {{ object.username }}</p>
                <p><strong>Last login:</strong> 
                    {% if object.last_login %}
                        {{ object.last_login|date:"M d, Y H:i" }}
                    {% else %}
                        Never
                    {% endif %}
                </p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
