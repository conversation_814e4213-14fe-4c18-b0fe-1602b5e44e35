{% extends 'base.html' %}
{% load currency_filters %}

{% block title %}Dashboard - Water Billing Management System{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block content %}
<!-- Info boxes -->
<div class="row">
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box">
            <span class="info-box-icon bg-info elevation-1"><i class="fas fa-users"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Total Clients</span>
                <span class="info-box-number">{{ total_clients }}</span>
            </div>
        </div>
    </div>
    
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-success elevation-1"><i class="fas fa-tags"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Categories</span>
                <span class="info-box-number">{{ total_categories }}</span>
            </div>
        </div>
    </div>
    
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-file-invoice-dollar"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">{{ current_month }} Billings</span>
                <span class="info-box-number">{{ total_billings }}</span>
            </div>
        </div>
    </div>
    
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-rupee-sign"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Total Revenue</span>
                <span class="info-box-number">{{ total_revenue|currency }}</span>
            </div>
        </div>
    </div>
</div>

<!-- Billing Status Row -->
<div class="row">
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box">
            <span class="info-box-icon bg-success elevation-1"><i class="fas fa-check-circle"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Paid Bills</span>
                <span class="info-box-number">{{ paid_billings }}</span>
            </div>
        </div>
    </div>
    
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-clock"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Pending Bills</span>
                <span class="info-box-number">{{ pending_billings }}</span>
            </div>
        </div>
    </div>
    
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-exclamation-triangle"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Overdue Bills</span>
                <span class="info-box-number">{{ overdue_billings }}</span>
            </div>
        </div>
    </div>
    
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-primary elevation-1"><i class="fas fa-user-cog"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Active Users</span>
                <span class="info-box-number">{{ total_users }}</span>
            </div>
        </div>
    </div>
</div>

<!-- Recent Billings -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Recent Billings</h3>
                <div class="card-tools">
                    <a href="{% url 'billing:monthly_report' %}" class="btn btn-tool btn-success">
                        <i class="fas fa-chart-bar"></i> Monthly Reports
                    </a>
                    <a href="{% url 'billing:billing_list' %}" class="btn btn-tool">
                        <i class="fas fa-eye"></i> View All
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Client</th>
                                <th>Billing Month</th>
                                <th>Consumption</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for billing in recent_billings %}
                            <tr>
                                <td>{{ billing.client.get_full_name }}</td>
                                <td>{{ billing.billing_month|date:"F Y" }}</td>
                                <td>{{ billing.consumption|format_consumption }}</td>
                                <td>{{ billing.total_amount|currency }}</td>
                                <td>
                                    {% if billing.status == 'paid' %}
                                        <span class="badge badge-success">Paid</span>
                                    {% elif billing.status == 'pending' %}
                                        <span class="badge badge-warning">Pending</span>
                                    {% else %}
                                        <span class="badge badge-danger">Overdue</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'billing:billing_detail' billing.pk %}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center">No recent billings found.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Chart -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Monthly Revenue Trend</h3>
            </div>
            <div class="card-body" style="height: 400px; position: relative;">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const ctx = document.getElementById('revenueChart');
    if (ctx) {
        const monthlyData = JSON.parse('{{ monthly_data|escapejs }}');
        console.log('Monthly Data:', monthlyData); // Debug log

        if (monthlyData && monthlyData.length > 0) {
            const labels = monthlyData.map(item => item.month);
            const amounts = monthlyData.map(item => parseFloat(item.total_amount) || 0);

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Revenue (₹)',
                        data: amounts,
                        borderColor: 'rgb(23, 162, 184)',
                        backgroundColor: 'rgba(23, 162, 184, 0.1)',
                        borderWidth: 3,
                        pointBackgroundColor: 'rgb(23, 162, 184)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'Revenue: ₹' + context.parsed.y.toLocaleString('en-IN');
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '₹' + value.toLocaleString('en-IN');
                                }
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    }
                }
            });
        } else {
            // Show message when no data available
            ctx.getContext('2d').font = '16px Arial';
            ctx.getContext('2d').fillStyle = '#666';
            ctx.getContext('2d').textAlign = 'center';
            ctx.getContext('2d').fillText('No revenue data available', ctx.width/2, ctx.height/2);
        }
    }
});
</script>
{% endblock %}
