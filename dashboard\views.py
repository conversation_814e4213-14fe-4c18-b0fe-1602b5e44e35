from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
import json
from clients.models import Client
from billing.models import Billing
from categories.models import Category
from accounts.models import User

@login_required
def home(request):
    # Get current month and year
    current_date = timezone.now()
    current_month = current_date.month
    current_year = current_date.year

    # Dashboard statistics
    total_clients = Client.objects.filter(status=True).count()
    total_categories = Category.objects.filter(status=True).count()
    total_users = User.objects.filter(is_active=True).count()

    # Billing statistics
    current_month_billings = Billing.objects.filter(
        billing_month__month=current_month,
        billing_month__year=current_year
    )

    total_billings = current_month_billings.count()
    pending_billings = current_month_billings.filter(status='pending').count()
    paid_billings = current_month_billings.filter(status='paid').count()
    overdue_billings = current_month_billings.filter(status='overdue').count()

    # Revenue statistics
    total_revenue = current_month_billings.aggregate(
        total=Sum('total_amount')
    )['total'] or 0

    paid_revenue = current_month_billings.filter(status='paid').aggregate(
        total=Sum('total_amount')
    )['total'] or 0

    pending_revenue = current_month_billings.filter(status='pending').aggregate(
        total=Sum('total_amount')
    )['total'] or 0

    # Recent billings
    recent_billings = Billing.objects.select_related('client', 'client__category').order_by('-created_at')[:10]

    # Monthly billing data for chart (last 6 months)
    monthly_data = []
    for i in range(6):
        date = current_date - timedelta(days=30*i)
        month_billings = Billing.objects.filter(
            billing_month__month=date.month,
            billing_month__year=date.year
        )
        total_amount = month_billings.aggregate(total=Sum('total_amount'))['total'] or 0
        monthly_data.append({
            'month': date.strftime('%B %Y'),
            'total_amount': float(total_amount),  # Convert Decimal to float for JSON serialization
            'count': month_billings.count()
        })

    monthly_data.reverse()

    context = {
        'total_clients': total_clients,
        'total_categories': total_categories,
        'total_users': total_users,
        'total_billings': total_billings,
        'pending_billings': pending_billings,
        'paid_billings': paid_billings,
        'overdue_billings': overdue_billings,
        'total_revenue': total_revenue,
        'paid_revenue': paid_revenue,
        'pending_revenue': pending_revenue,
        'recent_billings': recent_billings,
        'monthly_data': json.dumps(monthly_data),
        'current_month': current_date.strftime('%B %Y'),
    }

    return render(request, 'dashboard/home.html', context)
