from django.core.management.base import BaseCommand
from django.db import connection
from django.conf import settings
import os

class Command(BaseCommand):
    help = 'Setup Supabase database for Django Water Billing Management System'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clean',
            action='store_true',
            help='Clean existing conflicting tables (WARNING: This will delete data!)',
        )
        parser.add_argument(
            '--check',
            action='store_true',
            help='Check current database connection and tables',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔧 Supabase Setup for Water Billing Management System'))
        self.stdout.write('=' * 60)

        if options['check']:
            self.check_database()
            return

        if not settings.DATABASES['default']['ENGINE'] == 'django.db.backends.postgresql':
            self.stdout.write(self.style.ERROR('❌ PostgreSQL not enabled!'))
            self.stdout.write('Please set USE_POSTGRESQL=True in your .env file')
            return

        try:
            with connection.cursor() as cursor:
                self.stdout.write('🔍 Checking database connection...')
                
                # Test connection
                cursor.execute("SELECT version();")
                version = cursor.fetchone()[0]
                self.stdout.write(self.style.SUCCESS(f'✅ Connected to PostgreSQL: {version}'))

                # Check existing tables
                cursor.execute("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_type = 'BASE TABLE'
                    ORDER BY table_name;
                """)
                
                existing_tables = [row[0] for row in cursor.fetchall()]
                
                if existing_tables:
                    self.stdout.write(f'📋 Found {len(existing_tables)} existing tables:')
                    for table in existing_tables:
                        self.stdout.write(f'   - {table}')
                
                # Check for conflicting tables
                django_tables = ['users', 'clients', 'categories', 'billings']
                conflicts = [table for table in django_tables if table in existing_tables]
                
                if conflicts:
                    self.stdout.write(self.style.WARNING(f'⚠️  Found {len(conflicts)} conflicting tables:'))
                    for table in conflicts:
                        self.stdout.write(self.style.WARNING(f'   - {table}'))
                    
                    if options['clean']:
                        self.stdout.write(self.style.WARNING('🗑️  Cleaning conflicting tables...'))
                        self.clean_tables(cursor, conflicts)
                    else:
                        self.stdout.write('')
                        self.stdout.write(self.style.WARNING('To resolve conflicts, run:'))
                        self.stdout.write(self.style.WARNING('python manage.py setup_supabase --clean'))
                        self.stdout.write('')
                        self.stdout.write(self.style.ERROR('WARNING: --clean will delete existing data!'))
                        return
                
                self.stdout.write(self.style.SUCCESS('✅ Database ready for Django migrations!'))
                self.stdout.write('')
                self.stdout.write('Next steps:')
                self.stdout.write('1. python manage.py migrate')
                self.stdout.write('2. python manage.py create_initial_data')
                self.stdout.write('3. Test client creation')

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Database connection failed: {str(e)}'))
            self.stdout.write('')
            self.stdout.write('Please check your Supabase configuration in .env:')
            self.stdout.write('- DB_HOST')
            self.stdout.write('- DB_NAME') 
            self.stdout.write('- DB_USER')
            self.stdout.write('- DB_PASSWORD')
            self.stdout.write('- DB_PORT')

    def check_database(self):
        """Check current database status"""
        try:
            with connection.cursor() as cursor:
                # Get database info
                cursor.execute("SELECT current_database(), current_user, version();")
                db_name, user, version = cursor.fetchone()
                
                self.stdout.write(self.style.SUCCESS('📊 Database Information:'))
                self.stdout.write(f'   Database: {db_name}')
                self.stdout.write(f'   User: {user}')
                self.stdout.write(f'   Version: {version}')
                
                # List all tables
                cursor.execute("""
                    SELECT table_name, table_type
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                    ORDER BY table_name;
                """)
                
                tables = cursor.fetchall()
                self.stdout.write(f'\n📋 Tables ({len(tables)} total):')
                for table_name, table_type in tables:
                    self.stdout.write(f'   - {table_name} ({table_type})')

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error: {str(e)}'))

    def clean_tables(self, cursor, tables):
        """Clean conflicting tables"""
        for table in tables:
            try:
                # First, try to backup the table
                backup_table = f'backup_{table}_{self.get_timestamp()}'
                cursor.execute(f'CREATE TABLE {backup_table} AS SELECT * FROM {table};')
                self.stdout.write(self.style.SUCCESS(f'✅ Backed up {table} to {backup_table}'))
                
                # Drop the original table
                cursor.execute(f'DROP TABLE {table} CASCADE;')
                self.stdout.write(self.style.SUCCESS(f'✅ Dropped table {table}'))
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'❌ Error cleaning {table}: {str(e)}'))

    def get_timestamp(self):
        """Get current timestamp for backup table names"""
        from datetime import datetime
        return datetime.now().strftime('%Y%m%d_%H%M%S')
