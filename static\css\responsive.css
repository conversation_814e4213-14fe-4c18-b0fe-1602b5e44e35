/* Responsive Design Enhancements for Water Billing Management System */

/* Mobile First Approach */
@media (max-width: 576px) {
    /* Small devices (landscape phones, 576px and down) */
    
    .login-box {
        width: 95%;
        margin: 10px auto;
    }
    
    .card-header h3 {
        font-size: 1.1rem;
    }
    
    .info-box {
        margin-bottom: 10px;
    }
    
    .info-box-number {
        font-size: 1.2rem;
    }
    
    .table-responsive {
        font-size: 0.85rem;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-bottom: 2px;
        border-radius: 4px !important;
        width: 100%;
    }
    
    .card-tools {
        margin-top: 10px;
    }
    
    .card-tools .btn {
        display: block;
        width: 100%;
        margin-bottom: 5px;
    }
    
    /* Hide some columns on mobile */
    .table .d-none-mobile {
        display: none !important;
    }
    
    /* Stack form elements */
    .form-row .col-md-6 {
        margin-bottom: 15px;
    }
    
    /* Sidebar adjustments */
    .main-sidebar {
        width: 250px;
    }
    
    .brand-text {
        font-size: 0.9rem;
    }
    
    /* Dashboard cards */
    .col-md-3 {
        margin-bottom: 15px;
    }
}

@media (max-width: 768px) {
    /* Medium devices (tablets, 768px and down) */
    
    .content-header h1 {
        font-size: 1.5rem;
    }
    
    .breadcrumb {
        font-size: 0.85rem;
    }
    
    .card-title {
        font-size: 1.1rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
    
    /* Form improvements */
    .form-group label {
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .form-control {
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    /* Modal adjustments */
    .modal-dialog {
        margin: 10px;
    }
    
    /* Navigation improvements */
    .nav-sidebar .nav-link {
        padding: 0.75rem 1rem;
    }
    
    .nav-sidebar .nav-link i {
        margin-right: 10px;
        width: 20px;
    }
}

@media (max-width: 992px) {
    /* Large devices (desktops, 992px and down) */
    
    .col-lg-3 {
        margin-bottom: 20px;
    }
    
    .info-box-content {
        padding-left: 10px;
    }
    
    /* Table improvements */
    .table-responsive {
        border: none;
    }
    
    /* Card spacing */
    .card {
        margin-bottom: 20px;
    }
    
    /* Button spacing */
    .btn + .btn {
        margin-left: 5px;
    }
}

/* Landscape orientation adjustments */
@media (max-height: 500px) and (orientation: landscape) {
    .login-page {
        padding: 20px 0;
    }
    
    .login-box {
        margin: 0 auto;
    }
    
    .content-header {
        padding: 10px 0;
    }
    
    .main-header {
        min-height: 50px;
    }
}

/* Print media queries */
@media print {
    .responsive-hide-print {
        display: none !important;
    }
    
    .table {
        font-size: 10pt;
    }
    
    .card {
        break-inside: avoid;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .brand-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
    
    .table {
        border-collapse: separate;
        border-spacing: 0;
    }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --card-bg: #2d2d2d;
    }
    
    /* Dark mode styles would go here */
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus improvements for keyboard navigation */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Touch device improvements */
@media (pointer: coarse) {
    .btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    .nav-link {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
    
    .table td,
    .table th {
        padding: 12px 8px;
    }
}

/* Container improvements */
.container-fluid {
    padding-left: 15px;
    padding-right: 15px;
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
}

/* Utility classes for responsive design */
.mobile-only {
    display: none;
}

.desktop-only {
    display: block;
}

@media (max-width: 768px) {
    .mobile-only {
        display: block;
    }
    
    .desktop-only {
        display: none;
    }
    
    .mobile-stack {
        display: block !important;
        width: 100% !important;
        margin-bottom: 10px;
    }
    
    .mobile-center {
        text-align: center !important;
    }
    
    .mobile-hide {
        display: none !important;
    }
}

/* Flexible grid system */
.flex-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.flex-grid > * {
    flex: 1;
    min-width: 250px;
}

@media (max-width: 768px) {
    .flex-grid > * {
        min-width: 100%;
    }
}

/* Improved spacing */
.section-spacing {
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .section-spacing {
        margin-bottom: 1rem;
    }
}

/* Better form layouts */
.form-row-responsive {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.form-row-responsive > * {
    flex: 1;
    min-width: 200px;
}

@media (max-width: 576px) {
    .form-row-responsive > * {
        min-width: 100%;
    }
}
