@echo off
echo ============================================
echo Restart Django Server for Network Access
echo ============================================
echo.

echo This script will restart your Django server with proper network binding
echo to allow external access from **************:8000 and **************:8080
echo.

REM Check if we're in the right directory
if not exist manage.py (
    echo ERROR: manage.py not found!
    echo Please run this script from your Django project directory.
    echo Expected: C:\WebApps\WaterBilling\
    echo Current: %CD%
    echo.
    pause
    exit /b 1
)

echo Project directory: %CD%
echo.

REM Check virtual environment
if not exist venv (
    echo ERROR: Virtual environment not found!
    echo Please ensure your project is properly deployed.
    pause
    exit /b 1
)

echo ============================================
echo STEP 1: STOPPING EXISTING SERVERS
echo ============================================

echo Checking for existing Django servers...
netstat -an | findstr :8000 >nul
if not errorlevel 1 (
    echo Found server running on port 8000
    echo.
    echo IMPORTANT: Please stop your current Django server
    echo Go to the Command Prompt window running Django and press Ctrl+C
    echo.
    echo Waiting for you to stop the server...
    :wait_for_stop
    timeout /t 3 /nobreak >nul
    netstat -an | findstr :8000 >nul
    if not errorlevel 1 (
        echo Server still running... waiting...
        goto :wait_for_stop
    )
    echo ✅ Server stopped
) else (
    echo ✅ No existing server found on port 8000
)

echo.
echo Checking for servers on port 8080...
netstat -an | findstr :8080 >nul
if not errorlevel 1 (
    echo Found server running on port 8080
    echo Please also stop any server running on port 8080
    echo.
    :wait_for_stop_8080
    timeout /t 3 /nobreak >nul
    netstat -an | findstr :8080 >nul
    if not errorlevel 1 (
        echo Server still running on 8080... waiting...
        goto :wait_for_stop_8080
    )
    echo ✅ Server on 8080 stopped
)

echo.
echo ============================================
echo STEP 2: CONFIGURING WINDOWS FIREWALL
echo ============================================

echo Creating Windows Firewall rules...
netsh advfirewall firewall add rule name="Django Water Billing 8000" dir=in action=allow protocol=TCP localport=8000 profile=any >nul 2>&1
netsh advfirewall firewall add rule name="Django Water Billing 8080" dir=in action=allow protocol=TCP localport=8080 profile=any >nul 2>&1

echo ✅ Firewall rules created for ports 8000 and 8080

echo.
echo ============================================
echo STEP 3: CHECKING NETWORK CONFIGURATION
echo ============================================

echo Your server's IP addresses:
ipconfig | findstr "IPv4"

echo.
echo Network interfaces that will be accessible:
netstat -an | findstr "LISTENING" | findstr ":80"

echo.
echo ============================================
echo STEP 4: STARTING SERVER WITH NETWORK ACCESS
echo ============================================

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo.
echo Testing database connection...
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'water_billing_system.settings')
import django
django.setup()
from django.db import connection
try:
    connection.ensure_connection()
    print('✅ Database connection: OK')
except Exception as e:
    print('❌ Database connection failed:', e)
    exit(1)
"

if errorlevel 1 (
    echo Database connection failed! Please check your internet connection.
    pause
    exit /b 1
)

echo.
echo ============================================
echo STARTING DJANGO SERVER FOR NETWORK ACCESS
echo ============================================
echo.
echo 🌐 Starting server with network binding...
echo.
echo Your Django server will be accessible at:
echo ✅ Local: http://localhost:8000
echo ✅ Network: http://**************:8000 (after router config)
echo ✅ Secondary: http://**************:8080 (after router config)
echo.
echo 🔐 Login credentials:
echo - Username: admin
echo - Password: admin123
echo.
echo 📋 Features available:
echo ✅ Add/manage clients → Saves to Supabase
echo ✅ Add/manage users → Saves to Supabase
echo ✅ Generate billing reports
echo ✅ Dashboard with real-time statistics
echo.
echo 🛑 To stop the server: Press Ctrl+C
echo.
echo ⚠️ IMPORTANT NOTES:
echo 1. Server is bound to 0.0.0.0:8000 (allows external access)
echo 2. Windows Firewall is configured
echo 3. For external access, router configuration is still required
echo.
echo Starting server now...
echo.

REM Start Django server with network binding
python manage.py runserver 0.0.0.0:8000

echo.
echo ============================================
echo SERVER STOPPED
echo ============================================
echo.
echo To restart the server with network access:
echo 1. cd %CD%
echo 2. venv\Scripts\activate
echo 3. python manage.py runserver 0.0.0.0:8000
echo.
echo Or run this script again: restart_server_network.bat
echo.
echo NETWORK ACCESS TROUBLESHOOTING:
echo ===============================
echo.
echo If external IPs still don't work:
echo 1. ✅ Django server: Working (bound to 0.0.0.0)
echo 2. ✅ Windows Firewall: Configured
echo 3. ⚠️ Router configuration: Required
echo.
echo Contact your network administrator to configure:
echo - Port forwarding: External ports → Server IP
echo - Firewall rules: Allow incoming connections
echo - NAT mapping: External IPs → Internal server IP
echo.
pause
