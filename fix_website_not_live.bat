@echo off
echo ============================================
echo Website Not Live - Diagnostic and Fix Tool
echo ============================================
echo.

echo This script will diagnose why your website is not accessible
echo and provide step-by-step fixes.
echo.

REM Check if we're in the right directory
if not exist manage.py (
    echo ERROR: manage.py not found!
    echo Please run this script from your Django project directory.
    echo Expected location: C:\WebApps\WaterBilling\
    echo.
    echo Current directory: %CD%
    echo.
    set /p newdir="Enter correct path to your Django project: "
    if not "%newdir%"=="" cd /d "%newdir%"
    if not exist manage.py (
        echo Still cannot find manage.py. Please check the path.
        pause
        exit /b 1
    )
)

echo Found Django project in: %CD%
echo.

echo ============================================
echo STEP 1: BASIC SYSTEM CHECK
echo ============================================

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found in PATH
    echo Please install Python or add it to PATH
    pause
    exit /b 1
) else (
    python --version
    echo ✅ Python is available
)

echo.
echo Checking virtual environment...
if not exist venv (
    echo ❌ Virtual environment not found
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created
) else (
    echo ✅ Virtual environment exists
)

echo.
echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
) else (
    echo ✅ Virtual environment activated
)

echo.
echo ============================================
echo STEP 2: DEPENDENCY CHECK
echo ============================================

echo Checking Django installation...
python -c "import django; print('Django version:', django.get_version())" 2>nul
if errorlevel 1 (
    echo ❌ Django not installed
    echo Installing Django...
    pip install Django==5.2.4
    if errorlevel 1 (
        echo Failed to install Django
        pause
        exit /b 1
    )
    echo ✅ Django installed
) else (
    echo ✅ Django is available
)

echo.
echo Checking other dependencies...
pip install python-decouple requests Pillow whitenoise
echo ✅ Basic dependencies installed

echo.
echo ============================================
echo STEP 3: DATABASE CONNECTION TEST
echo ============================================

echo Testing database connection...
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'water_billing_system.settings')
import django
django.setup()

try:
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute('SELECT 1')
        print('✅ Database connection successful')
except Exception as e:
    print('❌ Database connection failed:', str(e))
    print('This might be why your website is not working')
    exit(1)
"

if errorlevel 1 (
    echo.
    echo DATABASE CONNECTION FAILED!
    echo.
    echo Possible solutions:
    echo 1. Check internet connection
    echo 2. Verify Supabase credentials in .env file
    echo 3. Check if Supabase service is running
    echo.
    echo Let's check your .env file:
    if exist .env (
        echo Current .env settings:
        type .env | findstr "USE_POSTGRESQL\|DB_HOST\|DB_NAME\|DB_USER"
    ) else (
        echo ❌ .env file not found!
        echo Creating basic .env file...
        echo USE_POSTGRESQL=True > .env
        echo DB_HOST=aws-0-ap-south-1.pooler.supabase.com >> .env
        echo DB_NAME=postgres >> .env
        echo DB_USER=postgres.sedjytwnxfynqmisnksc >> .env
        echo DB_PASSWORD=Ankurtech@123 >> .env
        echo DB_PORT=5432 >> .env
        echo DEBUG=False >> .env
        echo ALLOWED_HOSTS=**************,**************,localhost,127.0.0.1,* >> .env
        echo ✅ Basic .env file created
    )
    echo.
    echo Try running this script again after fixing database connection.
    pause
    exit /b 1
)

echo.
echo ============================================
echo STEP 4: DJANGO SETUP
echo ============================================

echo Running database migrations...
python manage.py migrate
if errorlevel 1 (
    echo ❌ Migration failed
    echo This could be why your website is not working
    pause
    exit /b 1
) else (
    echo ✅ Database migrations completed
)

echo.
echo Collecting static files...
python manage.py collectstatic --noinput
if errorlevel 1 (
    echo ⚠️ Static files collection failed (website may look broken)
) else (
    echo ✅ Static files collected
)

echo.
echo Creating admin user (if needed)...
python manage.py shell -c "
from accounts.models import User
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123', user_type='admin')
    print('✅ Admin user created')
else:
    print('✅ Admin user already exists')
"

echo.
echo ============================================
echo STEP 5: PORT AND FIREWALL CHECK
echo ============================================

echo Checking if port 8000 is available...
netstat -an | findstr :8000 >nul
if not errorlevel 1 (
    echo ⚠️ Port 8000 is already in use
    echo Finding what's using port 8000...
    netstat -ano | findstr :8000
    echo.
    echo You can either:
    echo 1. Kill the process using the port
    echo 2. Use a different port (like 8080)
    echo.
    set /p choice="Use port 8080 instead? (Y/N): "
    if /i "%choice%"=="Y" (
        set PORT=8080
    ) else (
        set PORT=8000
    )
) else (
    echo ✅ Port 8000 is available
    set PORT=8000
)

echo.
echo Checking Windows Firewall...
echo Creating firewall rule for ports 8000 and 8080...
netsh advfirewall firewall add rule name="Django Water Billing" dir=in action=allow protocol=TCP localport=8000,8080 >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Could not create firewall rule (may need admin rights)
    echo Please manually allow ports 8000 and 8080 in Windows Firewall
) else (
    echo ✅ Firewall rule created
)

echo.
echo ============================================
echo STEP 6: STARTING THE SERVER
echo ============================================

echo Starting Django development server on port %PORT%...
echo.
echo ✅ Your website should now be accessible at:
echo - Local: http://localhost:%PORT%
echo - Network: http://**************:%PORT%
echo - Secondary: http://**************:%PORT%
echo.
echo 🔐 Login credentials:
echo - Username: admin
echo - Password: admin123
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the server
python manage.py runserver 0.0.0.0:%PORT%

echo.
echo Server stopped.
echo.
echo ============================================
echo TROUBLESHOOTING TIPS
echo ============================================
echo.
echo If website is still not accessible:
echo.
echo 1. LOCAL ACCESS TEST:
echo    - Open browser on this server
echo    - Go to: http://localhost:%PORT%
echo    - Should show login page
echo.
echo 2. NETWORK ACCESS TEST:
echo    - From another computer
echo    - Go to: http://**************:%PORT%
echo    - If this fails, check router/network settings
echo.
echo 3. FIREWALL CHECK:
echo    - Open Windows Firewall (wf.msc)
echo    - Look for "Django Water Billing" rule
echo    - Make sure it allows inbound connections
echo.
echo 4. ROUTER CONFIGURATION:
echo    - Contact your network administrator
echo    - Ask them to forward external ports to this server
echo    - Port %PORT% should forward to this server's IP
echo.
echo 5. CHECK SERVER LOGS:
echo    - Look at the output above for any error messages
echo    - Common issues: database connection, port conflicts
echo.
echo To restart the server:
echo cd %CD%
echo venv\Scripts\activate
echo python manage.py runserver 0.0.0.0:%PORT%
echo.
pause
