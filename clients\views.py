from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from .models import Client
from .forms import ClientForm

class ClientListView(LoginRequiredMixin, ListView):
    model = Client
    template_name = 'clients/client_list.html'
    context_object_name = 'clients'
    paginate_by = 10

    def get_queryset(self):
        queryset = Client.objects.select_related('category').order_by('lastname', 'firstname')
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(code__icontains=search) |
                Q(firstname__icontains=search) |
                Q(lastname__icontains=search) |
                Q(email__icontains=search) |
                Q(meter_number__icontains=search)
            )
        return queryset

class ClientCreateView(LoginRequiredMixin, CreateView):
    model = Client
    form_class = ClientForm
    template_name = 'clients/client_form.html'
    success_url = reverse_lazy('clients:client_list')

    def form_valid(self, form):
        messages.success(self.request, 'Client created successfully.')
        return super().form_valid(form)

class ClientUpdateView(LoginRequiredMixin, UpdateView):
    model = Client
    form_class = ClientForm
    template_name = 'clients/client_form.html'
    success_url = reverse_lazy('clients:client_list')

    def form_valid(self, form):
        messages.success(self.request, 'Client updated successfully.')
        return super().form_valid(form)

class ClientDeleteView(LoginRequiredMixin, DeleteView):
    model = Client
    template_name = 'clients/client_confirm_delete.html'
    success_url = reverse_lazy('clients:client_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Client deleted successfully.')
        return super().delete(request, *args, **kwargs)

@login_required
def client_detail(request, pk):
    client = get_object_or_404(Client, pk=pk)
    billings = client.billings.all().order_by('-billing_month')
    context = {
        'client': client,
        'billings': billings,
        'billing_count': billings.count(),
    }
    return render(request, 'clients/client_detail.html', context)

@login_required
def client_billing_history(request, pk):
    client = get_object_or_404(Client, pk=pk)
    billings = client.billings.all().order_by('-billing_month')
    context = {
        'client': client,
        'billings': billings,
    }
    return render(request, 'clients/client_billing_history.html', context)
