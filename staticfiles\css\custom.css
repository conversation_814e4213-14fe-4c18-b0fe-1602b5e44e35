/* Water Billing Management System - Custom CSS */

/* Brand Colors */
:root {
    --primary-blue: #007bff;
    --water-blue: #17a2b8;
    --success-green: #28a745;
    --warning-orange: #ffc107;
    --danger-red: #dc3545;
    --dark-blue: #343a40;
    --light-gray: #f8f9fa;
}

/* Login Page Styling */
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-box {
    width: 360px;
    margin: 0 auto;
}

.login-box .card {
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    border: none;
    border-radius: 10px;
}

.login-box .card-header {
    background: linear-gradient(45deg, var(--primary-blue), var(--water-blue));
    color: white;
    border-radius: 10px 10px 0 0;
    text-align: center;
    padding: 20px;
}

.login-box .card-header h3 {
    margin: 10px 0;
    font-weight: 300;
}

.login-box .card-header .h1 {
    font-size: 3rem;
    margin-bottom: 10px;
}

/* Dashboard Enhancements */
.info-box {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.info-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.info-box-icon {
    border-radius: 50%;
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Water-themed Icons */
.water-icon {
    color: var(--water-blue);
}

.billing-icon {
    color: var(--success-green);
}

.client-icon {
    color: var(--primary-blue);
}

.category-icon {
    color: var(--warning-orange);
}

/* Card Enhancements */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-bottom: 2px solid var(--primary-blue);
    font-weight: 600;
}

/* Table Enhancements */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(45deg, var(--primary-blue), var(--water-blue));
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(23, 162, 184, 0.1);
}

/* Button Enhancements */
.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-blue), var(--water-blue));
    border: none;
}

.btn-success {
    background: linear-gradient(45deg, var(--success-green), #20c997);
    border: none;
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-orange), #fd7e14);
    border: none;
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-red), #e83e8c);
    border: none;
}

/* Badge Enhancements */
.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
    border-radius: 15px;
}

/* Form Enhancements */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--water-blue);
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

/* Sidebar Enhancements */
.main-sidebar {
    background: linear-gradient(180deg, #343a40 0%, #495057 100%);
}

.nav-sidebar .nav-link {
    border-radius: 8px;
    margin: 2px 8px;
    transition: all 0.3s ease;
}

.nav-sidebar .nav-link:hover {
    background: rgba(255,255,255,0.1);
    transform: translateX(5px);
}

.nav-sidebar .nav-link.active {
    background: linear-gradient(45deg, var(--primary-blue), var(--water-blue));
    color: white;
}

/* Brand Logo */
.brand-link {
    background: linear-gradient(45deg, var(--primary-blue), var(--water-blue));
    color: white !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.brand-link:hover {
    background: linear-gradient(45deg, var(--water-blue), var(--primary-blue));
    color: white !important;
}

.brand-image {
    font-size: 1.5rem;
    margin-right: 10px;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-success {
    border-left-color: var(--success-green);
    background: linear-gradient(45deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
}

.alert-info {
    border-left-color: var(--water-blue);
    background: linear-gradient(45deg, rgba(23, 162, 184, 0.1), rgba(0, 123, 255, 0.1));
}

.alert-warning {
    border-left-color: var(--warning-orange);
    background: linear-gradient(45deg, rgba(255, 193, 7, 0.1), rgba(253, 126, 20, 0.1));
}

.alert-danger {
    border-left-color: var(--danger-red);
    background: linear-gradient(45deg, rgba(220, 53, 69, 0.1), rgba(232, 62, 140, 0.1));
}

/* Timeline Enhancements */
.timeline {
    position: relative;
    margin: 0 0 30px 0;
    padding: 0;
    list-style: none;
}

.timeline:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 31px;
    width: 2px;
    background: var(--water-blue);
}

.timeline > li {
    position: relative;
    margin-right: 10px;
    margin-bottom: 15px;
}

.timeline > li:before,
.timeline > li:after {
    content: " ";
    display: table;
}

.timeline > li:after {
    clear: both;
}

.timeline > li > .timeline-item {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 8px;
    margin-top: 10px;
    background: #fff;
    color: #444;
    margin-left: 60px;
    margin-right: 15px;
    padding: 0;
    position: relative;
}

.timeline > li > .timeline-item > .time-label {
    border-radius: 4px;
    background-color: var(--water-blue);
    color: #fff;
    display: block;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    padding: 5px;
    text-align: center;
}

/* Print Styles */
@media print {
    .no-print,
    .btn,
    .card-tools,
    .sidebar,
    .main-header,
    .main-footer,
    .content-header {
        display: none !important;
    }
    
    .content-wrapper {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    body {
        font-size: 12px;
    }
    
    .table {
        font-size: 11px;
    }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .login-box {
        width: 90%;
        margin: 20px auto;
    }
    
    .info-box {
        margin-bottom: 15px;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 5px;
        border-radius: 25px !important;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--water-blue);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-blue);
}
