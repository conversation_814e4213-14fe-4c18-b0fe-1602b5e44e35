from django.urls import path
from . import views

app_name = 'billing'

urlpatterns = [
    path('', views.BillingListView.as_view(), name='billing_list'),
    path('create/', views.BillingCreateView.as_view(), name='billing_create'),
    path('<int:pk>/', views.billing_detail, name='billing_detail'),
    path('<int:pk>/update/', views.BillingUpdateView.as_view(), name='billing_update'),
    path('<int:pk>/delete/', views.BillingDeleteView.as_view(), name='billing_delete'),
    path('reports/monthly/', views.monthly_report, name='monthly_report'),
    path('reports/monthly/print/', views.print_monthly_report, name='print_monthly_report'),
    path('reports/monthly/export/csv/', views.export_monthly_report_csv, name='export_monthly_report_csv'),
    path('reports/monthly/export/json/', views.export_monthly_report_json, name='export_monthly_report_json'),
]
