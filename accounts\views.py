from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from .models import User, SystemInfo
from .forms import UserForm, SystemInfoForm, RegisterForm

def login_view(request):
    if request.user.is_authenticated:
        return redirect('dashboard:home')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            messages.success(request, f'Welcome back, {user.get_full_name() or user.username}!')
            return redirect('dashboard:home')
        else:
            messages.error(request, 'Invalid username or password.')

    return render(request, 'accounts/login.html')

def login_test_view(request):
    """Test view to check template rendering"""
    return render(request, 'accounts/login_test.html')

class RegisterView(CreateView):
    model = User
    form_class = RegisterForm
    template_name = 'accounts/register.html'
    
    def form_valid(self, form):
        user = form.save()
        messages.success(self.request, 'Account created successfully! You can now log in.')
        return redirect('accounts:login')
    
    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            return redirect('dashboard:home')
        return super().dispatch(request, *args, **kwargs)

@login_required
def logout_view(request):
    logout(request)
    messages.success(request, 'You have been logged out successfully.')
    return redirect('accounts:login')

def is_admin(user):
    """Check if user is admin"""
    return user.is_authenticated and user.user_type == 'admin'

def is_staff_or_admin(user):
    """Check if user is staff or admin"""
    return user.is_authenticated and user.user_type in ['admin', 'staff']

class AdminRequiredMixin(UserPassesTestMixin):
    """Mixin to require admin access"""
    def test_func(self):
        return self.request.user.is_authenticated and self.request.user.user_type == 'admin'

    def handle_no_permission(self):
        messages.error(self.request, 'You need admin privileges to access this page.')
        return redirect('dashboard:home')

class StaffOrAdminRequiredMixin(UserPassesTestMixin):
    """Mixin to require staff or admin access"""
    def test_func(self):
        return self.request.user.is_authenticated and self.request.user.user_type in ['admin', 'staff']

    def handle_no_permission(self):
        messages.error(self.request, 'You need staff or admin privileges to access this page.')
        return redirect('dashboard:home')

class UserListView(LoginRequiredMixin, AdminRequiredMixin, ListView):
    model = User
    template_name = 'accounts/user_list.html'
    context_object_name = 'users'
    paginate_by = 10

    def get_queryset(self):
        return User.objects.all().order_by('-date_joined')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user_type_stats'] = {
            'admin': User.objects.filter(user_type='admin').count(),
            'staff': User.objects.filter(user_type='staff').count(),
            'user': User.objects.filter(user_type='user').count(),
        }
        return context

class UserCreateView(LoginRequiredMixin, AdminRequiredMixin, CreateView):
    model = User
    form_class = UserForm
    template_name = 'accounts/user_form.html'
    success_url = reverse_lazy('accounts:user_list')

    def form_valid(self, form):
        messages.success(self.request, 'User created successfully.')
        return super().form_valid(form)

class UserUpdateView(LoginRequiredMixin, AdminRequiredMixin, UpdateView):
    model = User
    form_class = UserForm
    template_name = 'accounts/user_form.html'
    success_url = reverse_lazy('accounts:user_list')

    def form_valid(self, form):
        messages.success(self.request, 'User updated successfully.')
        return super().form_valid(form)

class UserDeleteView(LoginRequiredMixin, AdminRequiredMixin, DeleteView):
    model = User
    template_name = 'accounts/user_confirm_delete.html'
    success_url = reverse_lazy('accounts:user_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'User deleted successfully.')
        return super().delete(request, *args, **kwargs)

@login_required
def profile_view(request):
    return render(request, 'accounts/profile.html', {'user': request.user})

@login_required
def update_profile(request):
    """Update user profile with role-based restrictions"""
    if request.method == 'POST':
        user = request.user

        # Basic profile fields that all users can update
        user.first_name = request.POST.get('first_name', '')
        user.last_name = request.POST.get('last_name', '')
        user.email = request.POST.get('email', '')
        user.phone = request.POST.get('phone', '')
        user.address = request.POST.get('address', '')

        # Only admin can change user_type and is_active for any user
        if user.user_type == 'admin':
            user_type = request.POST.get('user_type')
            if user_type and user_type in ['admin', 'staff', 'user']:
                user.user_type = user_type
            user.is_active = 'is_active' in request.POST

        # Handle password change
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')

        if new_password:
            if not user.check_password(current_password):
                messages.error(request, 'Current password is incorrect.')
                return render(request, 'accounts/update_profile.html', {'user': user})

            if new_password != confirm_password:
                messages.error(request, 'New passwords do not match.')
                return render(request, 'accounts/update_profile.html', {'user': user})

            user.set_password(new_password)

        try:
            user.save()
            messages.success(request, 'Profile updated successfully.')
        except Exception as e:
            messages.error(request, f'Error updating profile: {str(e)}')

        return redirect('accounts:profile')

    return render(request, 'accounts/update_profile.html', {'user': request.user})

@login_required
@user_passes_test(is_admin)
def system_info_view(request):
    system_info, created = SystemInfo.objects.get_or_create(pk=1)

    if request.method == 'POST':
        form = SystemInfoForm(request.POST, request.FILES, instance=system_info)
        if form.is_valid():
            form.save()
            messages.success(request, 'System information updated successfully.')
            return redirect('accounts:system_info')
    else:
        form = SystemInfoForm(instance=system_info)

    return render(request, 'accounts/system_info.html', {'form': form, 'system_info': system_info})