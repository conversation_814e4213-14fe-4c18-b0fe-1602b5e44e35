{% extends 'base.html' %}

{% block title %}{% if object %}Edit Category{% else %}Add Category{% endif %} - Water Billing Management System{% endblock %}

{% block page_title %}{% if object %}Edit Category{% else %}Add New Category{% endif %}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'categories:category_list' %}">Categories</a></li>
<li class="breadcrumb-item active">{% if object %}Edit{% else %}Add{% endif %}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">Category Information</h3>
            </div>
            <form method="post">
                {% csrf_token %}
                <div class="card-body">
                    <div class="form-group">
                        <label for="{{ form.name.id_for_label }}">Category Name *</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.description.id_for_label }}">Description</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.price_per_cubic_meter.id_for_label }}">Price per Cubic Meter (₹) *</label>
                        {{ form.price_per_cubic_meter }}
                        {% if form.price_per_cubic_meter.errors %}
                            <div class="text-danger">
                                {% for error in form.price_per_cubic_meter.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            {{ form.status }}
                            <label class="form-check-label" for="{{ form.status.id_for_label }}">
                                Active
                            </label>
                        </div>
                        {% if form.status.errors %}
                            <div class="text-danger">
                                {% for error in form.status.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> {% if object %}Update{% else %}Save{% endif %}
                    </button>
                    <a href="{% url 'categories:category_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">Help</h3>
            </div>
            <div class="card-body">
                <p><strong>Category Name:</strong> Enter a unique name for this billing category.</p>
                <p><strong>Description:</strong> Optional description to explain this category.</p>
                <p><strong>Price per Cubic Meter:</strong> Set the billing rate in Indian Rupees (₹) for water consumption in this category.</p>
                <p><strong>Active:</strong> Check to make this category available for new clients.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
