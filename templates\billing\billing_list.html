{% extends 'base.html' %}
{% load currency_filters %}

{% block title %}Billing - Water Billing Management System{% endblock %}

{% block page_title %}Billing Management{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item active">Billing</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Billing Records</h3>
                <div class="card-tools">
                    <div class="input-group input-group-sm" style="width: 300px;">
                        <form method="get" class="d-flex">
                            <input type="text" name="search" class="form-control" placeholder="Search by client..." value="{{ request.GET.search }}">
                            <select name="status" class="form-control ml-1">
                                <option value="">All Status</option>
                                {% for value, label in status_choices %}
                                    <option value="{{ value }}" {% if request.GET.status == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-default">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <a href="{% url 'billing:billing_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create New Billing
                    </a>
                    <a href="{% url 'billing:monthly_report' %}" class="btn btn-info">
                        <i class="fas fa-chart-bar"></i> Monthly Report
                    </a>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Client</th>
                                <th>Billing Month</th>
                                <th>Previous Reading</th>
                                <th>Current Reading</th>
                                <th>Consumption</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Due Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for billing in billings %}
                            <tr>
                                <td>
                                    <strong>{{ billing.client.code }}</strong><br>
                                    <small>{{ billing.client.get_full_name }}</small>
                                </td>
                                <td>{{ billing.billing_month|date:"F Y" }}</td>
                                <td>{{ billing.previous_reading|format_consumption }}</td>
                                <td>{{ billing.current_reading|format_consumption }}</td>
                                <td><strong>{{ billing.consumption|format_consumption }}</strong></td>
                                <td>
                                    <strong>{{ billing.total_amount|currency }}</strong>
                                    {% if billing.penalty > 0 %}
                                        <br><small class="text-danger">Penalty: {{ billing.penalty|currency }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if billing.status == 'paid' %}
                                        <span class="badge badge-success">Paid</span>
                                        {% if billing.paid_date %}
                                            <br><small class="text-muted">{{ billing.paid_date|date:"M d, Y" }}</small>
                                        {% endif %}
                                    {% elif billing.status == 'pending' %}
                                        <span class="badge badge-warning">Pending</span>
                                    {% else %}
                                        <span class="badge badge-danger">Overdue</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ billing.due_date|date:"M d, Y" }}
                                    {% if billing.status != 'paid' and billing.due_date < today %}
                                        <br><small class="text-danger">Overdue</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'billing:billing_detail' billing.pk %}" class="btn btn-sm btn-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'billing:billing_update' billing.pk %}" class="btn btn-sm btn-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'billing:billing_delete' billing.pk %}" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this billing?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="9" class="text-center">No billing records found.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <div class="row">
                    <div class="col-sm-12 col-md-5">
                        <div class="dataTables_info">
                            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-7">
                        <div class="dataTables_paginate paging_simple_numbers">
                            <ul class="pagination">
                                {% if page_obj.has_previous %}
                                    <li class="paginate_button page-item previous">
                                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" class="page-link">Previous</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="paginate_button page-item active">
                                            <a href="#" class="page-link">{{ num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="paginate_button page-item">
                                            <a href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" class="page-link">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="paginate_button page-item next">
                                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" class="page-link">Next</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
