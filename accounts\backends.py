from django.contrib.auth.backends import BaseBackend
from django.contrib.auth.hashers import check_password
from django.db import connection
import logging

logger = logging.getLogger(__name__)

class CustomAuthBackend(BaseBackend):
    """
    Custom authentication backend for minimal database structure.
    Works directly with the users table without Django's auth framework.
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        Authenticate user against the users table
        """
        if username is None or password is None:
            return None
        
        try:
            # Query the users table directly
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT id, username, email, password, first_name, last_name, 
                           is_active, is_staff, date_joined, last_login
                    FROM users 
                    WHERE username = %s AND is_active = true
                """, [username])
                
                row = cursor.fetchone()
                
                if row is None:
                    return None
                
                # Create a user object from the database row
                user_data = {
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'password': row[3],
                    'first_name': row[4],
                    'last_name': row[5],
                    'is_active': row[6],
                    'is_staff': row[7],
                    'date_joined': row[8],
                    'last_login': row[9],
                }
                
                # Check password
                if check_password(password, user_data['password']):
                    # Update last login
                    cursor.execute("""
                        UPDATE users 
                        SET last_login = CURRENT_TIMESTAMP 
                        WHERE id = %s
                    """, [user_data['id']])
                    
                    # Return a simple user object
                    return SimpleUser(user_data)
                
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return None
        
        return None
    
    def get_user(self, user_id):
        """
        Get user by ID
        """
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT id, username, email, password, first_name, last_name, 
                           is_active, is_staff, date_joined, last_login
                    FROM users 
                    WHERE id = %s AND is_active = true
                """, [user_id])
                
                row = cursor.fetchone()
                
                if row is None:
                    return None
                
                user_data = {
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'password': row[3],
                    'first_name': row[4],
                    'last_name': row[5],
                    'is_active': row[6],
                    'is_staff': row[7],
                    'date_joined': row[8],
                    'last_login': row[9],
                }
                
                return SimpleUser(user_data)
                
        except Exception as e:
            logger.error(f"Get user error: {str(e)}")
            return None


class SimpleUser:
    """
    Simple user class that mimics Django's User model
    for authentication purposes
    """
    
    def __init__(self, user_data):
        self.id = user_data['id']
        self.pk = user_data['id']
        self.username = user_data['username']
        self.email = user_data['email']
        self.password = user_data['password']
        self.first_name = user_data['first_name']
        self.last_name = user_data['last_name']
        self.is_active = user_data['is_active']
        self.is_staff = user_data['is_staff']
        self.date_joined = user_data['date_joined']
        self.last_login = user_data['last_login']
        self.is_authenticated = True
        self.is_anonymous = False
    
    def get_full_name(self):
        """Return the full name for the user"""
        full_name = f"{self.first_name} {self.last_name}".strip()
        return full_name if full_name else self.username
    
    def get_short_name(self):
        """Return the short name for the user"""
        return self.first_name if self.first_name else self.username
    
    def __str__(self):
        return self.username
    
    def has_perm(self, perm, obj=None):
        """
        Simple permission check - staff users have all permissions
        """
        return self.is_staff
    
    def has_perms(self, perm_list, obj=None):
        """
        Check if user has all permissions in the list
        """
        return self.is_staff
    
    def has_module_perms(self, app_label):
        """
        Check if user has permissions for the given app
        """
        return self.is_staff
    
    @property
    def is_superuser(self):
        """
        Staff users are considered superusers in our minimal system
        """
        return self.is_staff
