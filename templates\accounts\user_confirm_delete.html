{% extends 'base.html' %}

{% block title %}Delete User - Water Billing Management System{% endblock %}

{% block page_title %}Delete User{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'accounts:user_list' %}">Users</a></li>
<li class="breadcrumb-item active">Delete</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card card-danger">
            <div class="card-header">
                <h3 class="card-title">Confirm User Deletion</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                
                <p>Are you sure you want to delete the user <strong>"{{ object.username }}"</strong>?</p>
                
                <div class="card">
                    <div class="card-body">
                        <h5>User Details:</h5>
                        <ul>
                            <li><strong>Username:</strong> {{ object.username }}</li>
                            <li><strong>Full Name:</strong> {{ object.get_full_name|default:"Not provided" }}</li>
                            <li><strong>Email:</strong> {{ object.email|default:"Not provided" }}</li>
                            <li><strong>User Type:</strong> 
                                {% if object.user_type == 'admin' %}
                                    <span class="badge badge-danger">Admin</span>
                                {% else %}
                                    <span class="badge badge-info">Staff</span>
                                {% endif %}
                            </li>
                            <li><strong>Status:</strong> 
                                {% if object.is_active %}
                                    <span class="badge badge-success">Active</span>
                                {% else %}
                                    <span class="badge badge-secondary">Inactive</span>
                                {% endif %}
                            </li>
                            <li><strong>Last Login:</strong> 
                                {% if object.last_login %}
                                    {{ object.last_login|date:"M d, Y H:i" }}
                                {% else %}
                                    Never
                                {% endif %}
                            </li>
                            <li><strong>Date Joined:</strong> {{ object.date_joined|date:"M d, Y H:i" }}</li>
                        </ul>
                    </div>
                </div>
                
                {% if object.user_type == 'admin' %}
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <strong>Important:</strong> This is an Administrator account with full system access. 
                    Make sure there are other admin users available before deleting this account.
                </div>
                {% endif %}
                
                {% if object.created_billings.exists %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Note:</strong> This user has created {{ object.created_billings.count }} billing record(s). 
                    These records will remain in the system but will show this user as the creator.
                </div>
                {% endif %}
                
                {% if object == request.user %}
                <div class="alert alert-danger">
                    <i class="fas fa-ban"></i>
                    <strong>Error:</strong> You cannot delete your own account while logged in.
                </div>
                {% endif %}
            </div>
            
            <div class="card-footer">
                {% if object != request.user %}
                <form method="post">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Yes, Delete User
                    </button>
                    <a href="{% url 'accounts:user_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </form>
                {% else %}
                <a href="{% url 'accounts:user_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to User List
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
