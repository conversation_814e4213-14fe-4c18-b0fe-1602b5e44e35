from django.contrib import admin
from .models import Client

@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ('code', 'get_full_name', 'category', 'meter_number', 'status', 'created_at')
    list_filter = ('category', 'status', 'created_at')
    search_fields = ('code', 'firstname', 'lastname', 'email', 'meter_number')
    ordering = ('lastname', 'firstname')

    def get_full_name(self, obj):
        return obj.get_full_name()
    get_full_name.short_description = 'Full Name'
