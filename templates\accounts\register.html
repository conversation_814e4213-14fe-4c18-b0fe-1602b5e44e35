{% extends 'base.html' %}
{% load static %}

{% block title %}Register - Water Billing Management System{% endblock %}

{% block body_class %}register-page{% endblock %}

{% block extra_css %}
<style>
    body.register-page .main-header {
        display: none;
    }
    
    body.register-page {
        background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
    }
</style>
{% endblock %}

{% block login_content %}
<div class="register-container">
    <div class="register-card">
        <div class="card-header">
            <div class="logo-container">
                <img src="{% static 'img/logo/logo.jpeg' %}" alt="Water Billing Management System" 
                     class="register-logo">
            </div>
            <div class="title-container">
                <h1 class="water-billing-title">Water Billing Management</h1>
                <p class="powered-by">Powered by <strong>Ankur Technologies</strong></p>
            </div>
        </div>
        
        <div class="card-body">
            <h2 class="register-title">Create a new account</h2>
            
            {% if messages %}
                <div class="messages-container">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            <form method="post" class="register-form">
                {% csrf_token %}
                
                <div class="form-section">
                    <h3 class="section-title">Account Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.username.id_for_label }}">Username *</label>
                            <div class="input-group">
                                <span class="input-icon">
                                    <i class="fas fa-user"></i>
                                </span>
                                {{ form.username }}
                            </div>
                            {% if form.username.errors %}
                                <div class="form-error">
                                    {% for error in form.username.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.email.id_for_label }}">Email Address</label>
                            <div class="input-group">
                                <span class="input-icon">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                {{ form.email }}
                            </div>
                            {% if form.email.errors %}
                                <div class="form-error">
                                    {% for error in form.email.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.password1.id_for_label }}">Password *</label>
                            <div class="input-group">
                                <span class="input-icon">
                                    <i class="fas fa-lock"></i>
                                </span>
                                {{ form.password1 }}
                            </div>
                            {% if form.password1.errors %}
                                <div class="form-error">
                                    {% for error in form.password1.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.password2.id_for_label }}">Confirm Password *</label>
                            <div class="input-group">
                                <span class="input-icon">
                                    <i class="fas fa-lock"></i>
                                </span>
                                {{ form.password2 }}
                            </div>
                            {% if form.password2.errors %}
                                <div class="form-error">
                                    {% for error in form.password2.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h3 class="section-title">Personal Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.first_name.id_for_label }}">First Name</label>
                            <div class="input-group">
                                <span class="input-icon">
                                    <i class="fas fa-user-tag"></i>
                                </span>
                                {{ form.first_name }}
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.last_name.id_for_label }}">Last Name</label>
                            <div class="input-group">
                                <span class="input-icon">
                                    <i class="fas fa-user-tag"></i>
                                </span>
                                {{ form.last_name }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.phone.id_for_label }}">Phone Number</label>
                            <div class="input-group">
                                <span class="input-icon">
                                    <i class="fas fa-phone"></i>
                                </span>
                                {{ form.phone }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.address.id_for_label }}">Address</label>
                            <div class="input-group">
                                <span class="input-icon">
                                    <i class="fas fa-home"></i>
                                </span>
                                {{ form.address }}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h3 class="section-title">Account Type</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <div class="account-type-select">
                                {{ form.user_type }}
                                <i class="fas fa-chevron-down select-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-submit">
                    <button type="submit" class="btn-register">
                        REGISTER
                    </button>
                </div>
                
                <div class="login-link">
                    <p>
                        Already have an account? 
                        <a href="{% url 'accounts:login' %}" class="login-text">
                            Login here
                        </a>
                    </p>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .register-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%231a252f"/><path d="M0 50 Q25 30, 50 50 T100 50" stroke="%232c3e50" stroke-width="1" fill="none"/></svg>');
        background-size: cover;
    }
    
    .register-card {
        background: white;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        width: 100%;
        max-width: 800px;
        animation: fadeIn 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }
    
    .card-header {
        background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
        padding: 40px 20px 30px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .card-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
        transform: rotate(30deg);
    }
    
    .logo-container {
        margin-bottom: 20px;
        position: relative;
        z-index: 2;
    }
    
    .register-logo {
        width: 90px;
        height: 90px;
        border-radius: 14px;
        border: 3px solid #FFD700;
        background: white;
        padding: 5px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        object-fit: cover;
    }
    
    .title-container {
        position: relative;
        z-index: 2;
    }
    
    .water-billing-title {
        color: white;
        font-weight: 700;
        font-size: 28px;
        letter-spacing: 0.5px;
        margin-bottom: 8px;
        text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
        position: relative;
        display: inline-block;
    }
    
    .water-billing-title::after {
        content: "";
        position: absolute;
        bottom: -8px;
        left: 25%;
        width: 50%;
        height: 4px;
        background: #FFD700;
        border-radius: 2px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    
    .powered-by {
        color: rgba(255, 255, 255, 0.85);
        font-size: 16px;
        margin-top: 15px;
        letter-spacing: 0.5px;
    }
    
    .powered-by strong {
        color: #FFD700;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
    
    .card-body {
        padding: 35px 40px 40px;
    }
    
    .register-title {
        text-align: center;
        color: #2c3e50;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 30px;
        position: relative;
    }
    
    .register-title::after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 4px;
        background: #667eea;
        border-radius: 2px;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding: 25px;
        background: #f8f9fc;
        border-radius: 12px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }
    
    .section-title {
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eaeaea;
    }
    
    .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .form-row:last-child {
        margin-bottom: 0;
    }
    
    .form-group {
        flex: 1;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #444;
        font-weight: 500;
        font-size: 15px;
    }
    
    .input-group {
        display: flex;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        transition: all 0.3s;
        border: 1px solid #e2e5ec;
    }
    
    .input-group:focus-within {
        box-shadow: 0 6px 18px rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
        border-color: #667eea;
    }
    
    .input-icon {
        width: 50px;
        background: #f1f3f9;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #667eea;
        font-size: 18px;
    }
    
    .register-form input,
    .register-form select {
        flex: 1;
        padding: 15px;
        border: none;
        font-size: 16px;
        background: white;
        outline: none;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        height: 52px;
    }
    
    .register-form input::placeholder,
    .register-form select {
        color: #aaa;
        font-weight: 400;
    }
    
    .account-type-select {
        position: relative;
    }
    
    .account-type-select select {
        width: 100%;
        appearance: none;
        padding: 15px 50px 15px 15px;
        border: 1px solid #e2e5ec;
        border-radius: 10px;
        background: white;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        font-size: 16px;
        cursor: pointer;
    }
    
    .select-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        color: #667eea;
        pointer-events: none;
    }
    
    .form-error {
        color: #dc3545;
        font-size: 14px;
        margin-top: 6px;
    }
    
    .btn-register {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        width: 100%;
        padding: 16px;
        font-size: 18px;
        font-weight: 700;
        letter-spacing: 1px;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 6px 18px rgba(102, 126, 234, 0.45);
        text-transform: uppercase;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin-top: 20px;
    }
    
    .btn-register:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 22px rgba(102, 126, 234, 0.55);
    }
    
    .btn-register:active {
        transform: translateY(1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .login-link {
        text-align: center;
        margin-top: 25px;
        padding-top: 25px;
        border-top: 1px solid #eee;
    }
    
    .login-link p {
        color: #666;
        font-size: 16px;
        margin-bottom: 0;
    }
    
    .login-text {
        color: #667eea;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s;
        position: relative;
    }
    
    .login-text::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 2px;
        background: #667eea;
        transition: width 0.3s;
    }
    
    .login-text:hover {
        color: #5a6edc;
        text-decoration: none;
    }
    
    .login-text:hover::after {
        width: 100%;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(30px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .form-row {
            flex-direction: column;
            gap: 15px;
        }
        
        .card-body {
            padding: 25px 20px;
        }
        
        .form-section {
            padding: 20px;
        }
    }
    
    @media (max-width: 576px) {
        .register-card {
            max-width: 95%;
        }
        
        .card-header {
            padding: 30px 15px 25px;
        }
        
        .register-logo {
            width: 80px;
            height: 80px;
        }
        
        .water-billing-title {
            font-size: 24px;
        }
        
        .powered-by {
            font-size: 15px;
        }
        
        .register-title {
            font-size: 22px;
        }
        
        .register-form input,
        .register-form select {
            padding: 14px;
            font-size: 15px;
            height: 48px;
        }
        
        .btn-register {
            padding: 15px;
            font-size: 17px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Auto-hide alerts after 6 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 6000);
        
        // Add focus effects to inputs
        $('.register-form input').focus(function() {
            $(this).closest('.input-group').css({
                'box-shadow': '0 6px 18px rgba(102, 126, 234, 0.25)',
                'transform': 'translateY(-2px)',
                'border-color': '#667eea'
            });
        }).blur(function() {
            $(this).closest('.input-group').css({
                'box-shadow': '0 4px 12px rgba(102, 126, 234, 0.15)',
                'transform': 'translateY(0)',
                'border-color': '#e2e5ec'
            });
        });
        
        // Button hover effect
        $('.btn-register').hover(function() {
            $(this).css({
                'transform': 'translateY(-3px)',
                'box-shadow': '0 8px 22px rgba(102, 126, 234, 0.55)'
            });
        }, function() {
            $(this).css({
                'transform': 'translateY(0)',
                'box-shadow': '0 6px 18px rgba(102, 126, 234, 0.45)'
            });
        });
    });
</script>
{% endblock %}