<!-- accounts/register.html -->
{% extends 'base.html' %}
{% load static %}

{% block title %}Register - Water Billing Management System{% endblock %}

{% block login_content %}
<div class="login-page" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
    <div class="login-box">
        <div class="card card-outline card-primary">
            <div class="card-header text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px 8px 0 0;">
                <div class="mb-3">
                    <img src="{% static 'img/logo/logo.jpeg' %}" alt="Water Billing Management System" class="img-fluid" style="max-height: 60px; border-radius: 10px; border: 3px solid #FFD700;">
                </div>
                <h3 style="color: white; margin: 0;">
                    <b>Create Account</b>
                </h3>
                <p style="color: #E8F5E8; margin-top: 5px; font-size: 14px;">
                    Register for Water Billing System
                </p>
            </div>
            <div class="card-body">
                <p class="login-box-msg">Create a new account</p>
                
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.username.id_for_label }}">Username *</label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger">
                                        {% for error in form.username.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.email.id_for_label }}">Email Address</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger">
                                        {% for error in form.email.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.password1.id_for_label }}">Password *</label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                    <div class="text-danger">
                                        {% for error in form.password1.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.password2.id_for_label }}">Confirm Password *</label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                    <div class="text-danger">
                                        {% for error in form.password2.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.first_name.id_for_label }}">First Name</label>
                                {{ form.first_name }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.last_name.id_for_label }}">Last Name</label>
                                {{ form.last_name }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.phone.id_for_label }}">Phone Number</label>
                        {{ form.phone }}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.address.id_for_label }}">Address</label>
                        {{ form.address }}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.user_type.id_for_label }}">Account Type</label>
                        {{ form.user_type }}
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary btn-block">Register</button>
                        </div>
                    </div>
                </form>
                
                <div class="mt-3 text-center">
                    <p>Already have an account? <a href="{% url 'accounts:login' %}">Login here</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}