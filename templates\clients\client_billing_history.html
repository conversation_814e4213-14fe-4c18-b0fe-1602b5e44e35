{% extends 'base.html' %}
{% load currency_filters %}

{% block title %}{{ client.get_full_name }} - Billing History{% endblock %}

{% block page_title %}Billing History{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'clients:client_list' %}">Clients</a></li>
<li class="breadcrumb-item"><a href="{% url 'clients:client_detail' client.pk %}">{{ client.code }}</a></li>
<li class="breadcrumb-item active">Billing History</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Billing History for {{ client.get_full_name }} ({{ client.code }})</h3>
                <div class="card-tools">
                    <a href="{% url 'billing:billing_create' %}?client={{ client.pk }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create New Bill
                    </a>
                    <a href="{% url 'clients:client_detail' client.pk %}" class="btn btn-info">
                        <i class="fas fa-user"></i> Client Details
                    </a>
                    <a href="{% url 'clients:client_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Clients
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Client Summary -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="info-box">
                            <span class="info-box-icon bg-info"><i class="fas fa-user"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Client</span>
                                <span class="info-box-number">{{ client.code }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box">
                            <span class="info-box-icon bg-success"><i class="fas fa-tags"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Category</span>
                                <span class="info-box-number">{{ client.category.name }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box">
                            <span class="info-box-icon bg-warning"><i class="fas fa-tachometer-alt"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Meter</span>
                                <span class="info-box-number">{{ client.meter_number }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box">
                            <span class="info-box-icon bg-danger"><i class="fas fa-rupee-sign"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Rate/m³</span>
                                <span class="info-box-number">{{ client.category.price_per_cubic_meter|currency }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Billing Records Table -->
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Billing Month</th>
                                <th>Previous Reading</th>
                                <th>Current Reading</th>
                                <th>Consumption</th>
                                <th>Rate</th>
                                <th>Amount</th>
                                <th>Penalty</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Due Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for billing in billings %}
                            <tr>
                                <td><strong>{{ billing.billing_month|date:"F Y" }}</strong></td>
                                <td>{{ billing.previous_reading }} m³</td>
                                <td>{{ billing.current_reading }} m³</td>
                                <td><strong>{{ billing.consumption }} m³</strong></td>
                                <td>{{ billing.rate_per_cubic_meter|currency }}</td>
                                <td>{{ billing.amount|currency }}</td>
                                <td>
                                    {% if billing.penalty > 0 %}
                                        <span class="text-danger">{{ billing.penalty|currency }}</span>
                                    {% else %}
                                        ₹0.00
                                    {% endif %}
                                </td>
                                <td><strong>{{ billing.total_amount|currency }}</strong></td>
                                <td>
                                    {% if billing.status == 'paid' %}
                                        <span class="badge badge-success">Paid</span>
                                        {% if billing.paid_date %}
                                            <br><small class="text-muted">{{ billing.paid_date|date:"M d, Y" }}</small>
                                        {% endif %}
                                    {% elif billing.status == 'pending' %}
                                        <span class="badge badge-warning">Pending</span>
                                    {% else %}
                                        <span class="badge badge-danger">Overdue</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ billing.due_date|date:"M d, Y" }}
                                    {% if billing.status != 'paid' and billing.due_date < today %}
                                        <br><small class="text-danger">Overdue</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'billing:billing_detail' billing.pk %}" class="btn btn-sm btn-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'billing:billing_update' billing.pk %}" class="btn btn-sm btn-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="11" class="text-center">
                                    <p class="text-muted">No billing records found for this client.</p>
                                    <a href="{% url 'billing:billing_create' %}?client={{ client.pk }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Create First Bill
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        {% if billings %}
                        <tfoot>
                            <tr class="bg-light">
                                <th colspan="7">Total</th>
                                <th>{{ billings|length }} bills</th>
                                <th colspan="3"></th>
                            </tr>
                        </tfoot>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
