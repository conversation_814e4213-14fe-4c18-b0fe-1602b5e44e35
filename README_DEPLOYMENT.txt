# Water Billing Management System - Server Deployment

## Quick Start

1. Extract this package to: C:\WebApps\WaterBilling\
2. Run as Administrator: deploy_server.bat
3. Start the application: python manage.py runserver 0.0.0.0:8000

## Access URLs
- Primary: http://**************:8000
- Secondary: http://**************:8080

## Login Credentials
- Username: admin
- Password: admin123

## Support
- All data is stored in Supabase database
- Application runs on Windows Server
- Accessible from internet via configured IPs

## Troubleshooting
1. Check Windows Firewall allows ports 8000/8080
2. Verify router port forwarding is configured
3. Ensure Python 3.11+ is installed
4. Check .env file configuration

For detailed instructions, see DEPLOYMENT_GUIDE.md
