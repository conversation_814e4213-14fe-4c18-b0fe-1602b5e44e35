{% extends 'base.html' %}

{% block title %}Delete Client - Water Billing Management System{% endblock %}

{% block page_title %}Delete Client{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'clients:client_list' %}">Clients</a></li>
<li class="breadcrumb-item active">Delete</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card card-danger">
            <div class="card-header">
                <h3 class="card-title">Confirm Client Deletion</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                
                <p>Are you sure you want to delete the client <strong>"{{ object.get_full_name }}"</strong>?</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Client Information</h5>
                            </div>
                            <div class="card-body">
                                <ul>
                                    <li><strong>Code:</strong> {{ object.code }}</li>
                                    <li><strong>Name:</strong> {{ object.get_full_name }}</li>
                                    <li><strong>Category:</strong> {{ object.category.name }}</li>
                                    <li><strong>Meter Number:</strong> {{ object.meter_number }}</li>
                                    <li><strong>Contact:</strong> {{ object.contact|default:"Not provided" }}</li>
                                    <li><strong>Email:</strong> {{ object.email|default:"Not provided" }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Associated Data</h5>
                            </div>
                            <div class="card-body">
                                <ul>
                                    <li><strong>Billing Records:</strong> {{ object.billings.count }}</li>
                                    <li><strong>Account Status:</strong> 
                                        {% if object.status %}
                                            <span class="badge badge-success">Active</span>
                                        {% else %}
                                            <span class="badge badge-danger">Inactive</span>
                                        {% endif %}
                                    </li>
                                    <li><strong>Created:</strong> {{ object.created_at|date:"M d, Y" }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if object.billings.count > 0 %}
                <div class="alert alert-danger mt-3">
                    <i class="fas fa-exclamation-circle"></i>
                    <strong>Important:</strong> This client has {{ object.billings.count }} billing record(s) associated with it. 
                    Deleting this client will also delete all associated billing records and payment history.
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Recent Billing Records</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Month</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for billing in object.billings.all|slice:":5" %}
                                    <tr>
                                        <td>{{ billing.billing_month|date:"M Y" }}</td>
                                        <td>${{ billing.total_amount|floatformat:2 }}</td>
                                        <td>
                                            {% if billing.status == 'paid' %}
                                                <span class="badge badge-success">Paid</span>
                                            {% elif billing.status == 'pending' %}
                                                <span class="badge badge-warning">Pending</span>
                                            {% else %}
                                                <span class="badge badge-danger">Overdue</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% if object.billings.count > 5 %}
                        <p class="text-muted">... and {{ object.billings.count|add:"-5" }} more billing record(s)</p>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
            
            <div class="card-footer">
                <form method="post">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Yes, Delete Client and All Associated Data
                    </button>
                    <a href="{% url 'clients:client_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
